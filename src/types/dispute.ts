import { CurrencyType } from './utils';

export interface IRefundDetails {
  amount: string;
  status: string;
  currency: CurrencyType;
  destination: string;
  reference: string;
  reversal_reason: string;
  merchant_reference: string;
  metadata: {
    payout_automated: boolean;
    refunds_operation_account_id: number | null;
    trace_id: number | null;
  };
  status_reason: string | null;
  created_at: string | null;
  completed_at: string | null;
  channel: string;
  account: {
    name: string;
  };
  payment: {
    reference: string;
    customer: {
      name: string;
      email: string;
    };
  };
  payment_source: {
    id: number;
    reference: string;
    amount: string;
    amount_collected: string;
    payment_source_type: string;
    channel: string;
    payment_source_id: number | string | null;
    processor: string;
    processor_reference: string;
    settlement: {
      reference: string;
    };
  };
  source: {
    type: string;
    details: {
      bank_name: string;
      bank_slug: string;
      masked_pan: string;
      card_type: string;
      account_number: string | number;
      account_name: string;
    };
  };
}
export interface IChargeBackDetails {
  status_history: [
    {
      date: string;
      status: string;
    }
  ];
  account_id: number | null;
  processor_reference: string;
  processor: string;
  reference: string;
  payment_reference: string;
  payment_source_reference: string;
  batch_code: string;
  status: string;
  currency: CurrencyType;
  amount: string | number;
  approved_amount: string | number;
  deadline: string;
  payment_method: string;
  payment_reversal_reference: string;
  log_code: string | null;
  reason: string;
  merchant: string;
  merchant_email: string;
  created_at: string;
  account: {
    name: string;
    email: string;
  };
  source: {
    type: string;
    details: {
      masked_pan: string;
      card_type: string;
      bank_name: string;
      bank_slug: string;
      account_number: string | number;
      account_name: string;
    };
  };
  payment_source?: {
    channel: string;
    payment_source_type: string;
  };
  payment?: {
    reference: string;
    customer: {
      name: string;
      email: string;
    };
  };
}
export type DisputeRefundStatusType = 'manual' | 'pending"' | 'processing' | 'failed' | 'success';

export type ModalStepT = 'confirm' | 'init' | 'feedback' | 'resolve-chargeback' | 'confirm-chargeback';
