import { switchTrxnMessage } from '+utils';

import { CurrencyType, ISODateStringType, PCIDSSLevelType, RiskLevelType } from './utils';

export type CardBrandType = 'mastercard' | 'verve' | 'visa' | 'maestro' | 'discover';

export type DisputeType = 'refunds' | 'chargebacks' | 'reversals';

export type RefundStatusType = 'pending' | 'fully_paid' | 'partially_paid';

export type ChargebackStatusType =
  | 'pending'
  | 'processing'
  | 'accepted'
  | 'partially_accepted'
  | 'declined'
  | 'invalid'
  | 'pending_pre_arbitration'
  | 'processing_pre_arbitration'
  | 'delayed_pre_arbitration'
  | 'declined_pre_arbitration'
  | 'invalid_pre_arbitration'
  | 'accepted_pre_arbitration'
  | 'partially_accepted_pre_arbitration'
  | 'accepted_arbitration'
  | 'declined_arbitration';

export interface IChargebackTransactionDetails {
  reference: string;
  amount: string;
  status: ChargebackStatusType;
  category: string;
  accepted_amount: string;
  re_escalated_amount: number;
  description: string;
  transaction_reference: string;
  transaction_amount: number;
  currency: CurrencyType;
  transaction_type: 'card_transaction';
  transaction_date: ISODateStringType;
  escalation_date: ISODateStringType;
  re_escalation_date: ISODateStringType;
  expected_resolution_date: ISODateStringType;
  actual_resolution_date: ISODateStringType;
  processing_date: ISODateStringType;
  pre_arbitration_processing_date: ISODateStringType;
  pre_arbitration_actual_resolution_date: ISODateStringType;
  pre_arbitration_expected_resolution_date: ISODateStringType;
  status_history: Array<{ status: ChargebackStatusType; evidence: string; description: string; date: ISODateStringType }>;
  card_acceptor_name: string;
  card_acceptor_country: string;
  card_reference: string;
  card_brand: CardBrandType;
  card_expiry_date: ISODateStringType;
  cross_currency: CurrencyType;
  card_holder_name: string;
  card_type: 'virtual' | 'physical';
  last_four: string;
  first_six: string;
  merchant_name: string;
}

export type ConfirmActionType = 'confirmDeclareInvalid' | 'confirmUpdateStatus' | 'confirmRefund';

export type NonConfirmActionType = 'refundChargeback' | 'declareInvalid' | 'updateStatus';

export interface IChargebackActionModal {
  action: NonConfirmActionType | ConfirmActionType | 'processChargeback';
  close: () => void;
  chargebackDetails: {
    status: string;
    currency: CurrencyType;
    chargebackAmount?: string;
    reference: string;
    acceptedAmount: string;
  };
  refetchTransaction: () => void;
}

export type RefundType = {
  reference: string;
  amount: string;
  status: 'pending' | 'partially_paid' | 'fully_paid';
  description: string;
  date_completed: ISODateStringType;
};

export type ChargebackType = {
  reference: string;
  amount: string;
  accepted_amount: string;
  status: ChargebackStatusType;
  escalation_date: ISODateStringType;
  actual_resolution_date: ISODateStringType;
  pre_arbitration?: {
    amount: number;
    accepted_amount: number;
    escalation_date: ISODateStringType;
    processing_date: ISODateStringType;
    actual_resolution_date: ISODateStringType;
    expected_resolution_date: ISODateStringType;
  };
};

export interface ICardTransactionDetails {
  reference: string;
  amount: number;
  vat: number;
  fee: number;
  currency: CurrencyType;
  cross_currency: boolean;
  card_acceptor_name: string;
  card_acceptor_country: string;
  description: string;
  balance_after: number;
  type: 'card_creation' | 'card_transaction' | 'card_funding' | 'cross_currency' | 'card_withdrawal';
  status: keyof typeof switchTrxnMessage;
  date: ISODateStringType;
  card: {
    reference: string;
    type: 'virtual' | 'physical';
    currency: CurrencyType;
    first_six: string;
    last_four: string;
    provider: string;
    provider_reference: string;
    status: 'active' | 'inactive' | 'pending';
    brand: CardBrandType;
    expiration_date: ISODateStringType;
    created_at: ISODateStringType;
    reserved: boolean;
    card_holder: {
      first_name: string;
      last_name: string;
    };
  };
  refunds: Array<RefundType>;
  merchant_name: string;
  processor_original_transaction_id: string;
  chargeback: ChargebackType;
}

export type ChargebackUpdatePayloadType = {
  status?: string;
  reason?: string;
  description?: string;
  evidence?: string | null;
  accepted_amount?: string;
  declined_amount?: string;
};

export type ChargebackActionType = NonConfirmActionType | ConfirmActionType | 'processChargeback';

export type UpdateRefundStatusPayloadType = {
  status: RefundStatusType;
  amount?: number;
  description?: string;
};

export interface IUpdateRefundStatusModal {
  currentStatus: RefundStatusType | undefined;
  acceptedAmount?: string | number;
  chargebackAmount?: string;
  currency: CurrencyType;
  onClose: () => void;
  refundId: string;
  refetch: () => void;
}

type FeeType = 'funding' | 'issuance' | 'chargeback' | 'withdrawal' | 'subscription' | 'cross_currency' | 'security_reserve';
type CardAccessStatusType = 'active' | 'inactive' | 'suspended';
type LimitType = 'custom' | 'default';

type FeeMetaType = {
  type: 'percentage' | 'flat';
  amount: number;
  vat_inclusive: boolean;
  charge_interval: 'monthly' | 'yearly';
};

export type MerchantLimitsConfigType = {
  status: CardAccessStatusType;
  card_creation_enabled: boolean;
  risk_level_limit: {
    spending_limit: {
      type: LimitType;
      data: {
        daily_max: number;
        monthly_max: number;
        per_transaction_max: number;
      };
    };
    funding_limit: {
      type: LimitType;
      data: {
        daily_max: number;
        monthly_max: number;
        quarterly_max: number;
      };
    };
  };
  pcidss_level_limit: {
    type: LimitType;
    data: {
      yearly_issued_cards: number;
      yearly_transaction_count: number | 'limitless';
    };
  };
};

export type IssuingPartnerType = 'maplerad' | 'sudo' | 'paytrade' | 'passpoint';

export type IssuingMerchantDetailsResponseType = {
  reference: string;
  name: string;
  status: CardAccessStatusType;
  plan: {
    reference: string;
    name: string;
    currency: CurrencyType;
    min_payment_value: string;
    max_payment_value: string;
    reserved_card_min_payment_value: string;
    reserved_card_max_payment_value: string;
    monthly_card_limit: number;
    reserved_card_limit: number;
    card_type: 'virtual' | 'physical';
    fee: Record<'customer' | 'reserved', Record<FeeType, FeeMetaType>>;
    type: 'standard';
    active: boolean;
    createdAt: string;
    updatedAt: string;
  };
  wallet_balance: number;
  issued_cards: number;
  reserved_cards: number;
  kora_id: number;
  transactions_count: number;
  config: Record<'reserved' | 'customer', MerchantLimitsConfigType>;
  risk_level: RiskLevelType;
  pci_dss_level: PCIDSSLevelType;
  date_created: string;
  provider: Record<'customer' | 'reserved', IssuingPartnerType>;
  access_request_reference: Record<'customer' | 'reserved', string>;
};

export type MerchantPCIDSSLimitsType = MerchantLimitsConfigType['pcidss_level_limit']['data'];
export type MerchantFundingLimitsType = MerchantLimitsConfigType['risk_level_limit']['funding_limit']['data'];
export type MerchantSpendingLimitsType = MerchantLimitsConfigType['risk_level_limit']['spending_limit']['data'];

export type AllIssuanceMerchantsResponseType = Array<{
  reference: string;
  name: string;
  status: 'active' | 'inactive' | 'suspended';
  subscription_plan: string;
  risk_level: RiskLevelType;
  limit_config_type: 'default' | 'custom';
  merchant_created_at: string;
}>;

export type AllAccessRequestMerchantResponse = {
  reference: string;
  name: string;
  card_type: string;
  currency: CurrencyType;
  currency_access_status: 'enabled' | 'disabled';
  email: string;
  pci_dss_level: string;
  monthly_payment_value: string;
  risk_level: RiskLevelType;
  issuing: string;
  subscription_plan: string;
  status: string;
  request_date: string;
  merchant_kora_id: number;
};

export type AllAccessRequestMerchantsResponseType = AllAccessRequestMerchantResponse[];
