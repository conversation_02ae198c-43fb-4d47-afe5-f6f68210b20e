export type TabsContextType = {
  tabsId: string | number;
  isTabActive: (arg: string) => boolean;
  onSelectTab: (arg: string) => void;
};

export interface ITabs extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'> {
  children: React.ReactNode;
  defaultValue: string;
  className?: string;
  onChange?: (arg: string) => void;
}

export interface IShared {
  children?: React.ReactNode;
  className?: string;
}

export interface ITabList extends IShared, React.HTMLAttributes<HTMLDivElement> {}

export interface ITab extends IShared, React.ButtonHTMLAttributes<HTMLButtonElement> {
  value: string;
  onClick?: () => void;
}

export interface ITabPanel extends IShared {
  value: string;
}
