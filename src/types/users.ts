import { IModalProps } from '+containers/Dashboard/Shared/Modal';

export type EntityType =
  | 'dashboard'
  | 'merchants'
  | 'virtual_accounts'
  | 'pay-ins'
  | 'payouts'
  | 'payment_reversals'
  | 'settlements'
  | 'settlement_payouts'
  | 'webhooks'
  | 'audit_logs'
  | 'global_settings'
  | 'user'
  | 'role'
  | 'chargebacks'
  | 'transaction_settings'
  | 'refunds'
  | 'card_issuance'
  | 'reports'
  | 'partner_funding'
  | 'identity'
  | 'bank_transfer_settings_requests'
  | 'metrics';

export type PermissionModalType =
  | 'changeRole'
  | 'confirmPermission'
  | 'deleteExplicitPermission'
  | 'confirmInvitation'
  | 'updatePermission';

export type PermissionTableType = {
  name: string;
  key: EntityType;
};

export interface IRoleDetails {
  id: number;
  name: string;
  slug: string;
  type: string;
  category: 'system' | 'custom';
  created_by: null | string;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
  'createdBy.first_name': null;
  'createdBy.last_name': null;
  'createdBy.email': null;
  numberOfAssignees: number;
  'user_role.permissions'?: string[];
}

export interface IRole {
  id: number;
  name: string;
  slug: string;
  type: string;
  category: 'custom' | 'system';
  created_by: number;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  createdBy: {
    first_name: string;
    last_name: string;
    email: string;
  };
}

export interface IAssignees {
  userId: number;
  firstName: string;
  lastName: string;
  email: string;
  dateAssigned: string;
}

export interface IPermission {
  id: number;
  name: string;
  slug: string;
  type: 'admin';
  entity: 'merchants';
  description: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface IUserDetails {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  avatar: URL;
  last_login: string;
  created_at: string;
  'user_role.name': string;
  adminInvitation: {
    id: number;
    code: string;
    user_status: 'active' | 'inactive' | 'suspended';
    status_description: string | null;
    created_at: string;
  };
  adminRoles: {
    id: number;
    name: string;
    slug: string;
    type: 'admin';
    category: 'system' | 'custom';
    created_by: null;
    permissions: string[];
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    admin_user_role: {
      id: number;
      user_id: number;
      role_id: number;
      createdAt: string;
      updatedAt: string;
    };
  }[];
  adminPermissions: {
    id: number;
    user_id: number;
    permission_id: number;
    allowed: boolean;
    createdAt: string;
    updatedAt: string;
    permissionId: number;
    userId: number;
    permission: IPermission;
    slug?: string;
  }[];
}

export type ExplicitPermissionType = {
  entity: EntityType;
  slug: string;
  id: number;
  allowed: boolean;
};

export type PermissionColumnType = {
  id: number;
  name: string;
  slug: string;
  type: string;
  entity: EntityType;
  description: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  isExplicit: boolean;
};

export type SelectedPermissionType = {
  name: string;
  key: EntityType;
  children: {
    [k in EntityType]: PermissionColumnType[];
  }[];
};

export type UserModalType = 'delete' | 'suspend' | 'reactivate' | 'revoke' | 'reason';

export type IUserModalDataType = {
  modalType: UserModalType | null;
  modalInfo: (Partial<IModalProps> & { action?: () => void }) | null;
};

export interface IUserModalProps {
  setModal: (modalData: IUserModalDataType) => void;
  details: IUserDetails;
  from: string;
  setButtonState: (value: boolean) => void;
}

export interface IUser {
  id: number;
  admin_id: number;
  role_id: number;
  email: string;
  code: string;
  expiry_date: string;
  status: 'accepted' | 'pending' | 'expired';
  user_status: 'active' | 'inactive' | 'suspended';
  user_status_description: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    last_login: string;
    adminRoles: [
      {
        id: number;
        name: string;
        admin_user_role: {
          id: number;
          user_id: number;
          role_id: number;
          createdAt: string;
          updatedAt: string;
        };
      }
    ];
  };
  admin: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
  };
  user_role: {
    id: number;
    name: string;
    slug: string;
    type: 'admin';
    category: 'custom' | 'system';
    created_by: number;
    permissions: IPermission;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
  };
}
