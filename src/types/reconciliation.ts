export type ReconciliationHistoryType = {
  id: number;
  status: 'pending' | 'successful' | 'processing' | 'failed';
  created_at: string;
  description: string;
};

export type ReconciliationDataType = {
  kora_id: number;
  processor: string;
  start_date: string;
  end_date: string;
  payment_type: 'payin' | 'payout';
  processor_file_id: string;
};

export type CreateProcessorConfigDataType = {
  processor: string;
  payment_type: 'payin' | 'payout';
  primary_key_mappings: Array<{
    processor_report: string;
    internal_report: string;
  }>;
  comparison_key_mappings: Array<{
    processor_report: string;
    internal_report: string;
  }>;
};

export type UploadSettlementDataType = {
  file_name: string;
  bucket_name: string;
  category: 'settlement_file';
};
