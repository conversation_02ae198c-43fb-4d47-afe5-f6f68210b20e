export type ReconciliationHistoryType = {
  id: number;
  status: 'pending' | 'successful' | 'processing' | 'failed';
  created_at: string;
  description: string;
};

export type ReconciliationDataType = {
  processor: string;
  report_start_date: string;
  report_end_date: string;
  payment_type: 'payin' | 'payout';
  processor_file_id: string;
  processor_file_details: {
    key: string;
  };
};

export type CreateProcessorConfigDataType = {
  processor: string;
  payment_type: 'payin' | 'payout';
  primary_key_mappings: Array<{
    processor_report: string;
    internal_report: string;
  }>;
  comparison_key_mappings: Array<{
    processor_report: string;
    internal_report: string;
  }>;
};

export type UploadSettlementDataType = {
  file_name: string;
  bucket_name: string;
  category: 'settlement_file';
};

export type FileUploadDataValues = {
  category: string;
  createdAt: string;
  encoding: string;
  id: number;
  identifier: string;
  mime: string;
  original_name: string;
  path: string;
  updatedAt: string;
};

export type FileUploadResponseType = {
  data: {
    dataValues: FileUploadDataValues;
    isNewRecord: boolean;
    key: string;
    uniqno: number;
    _changed: Record<string, any>;
    _options: {
      isNewRecord: boolean;
      _schema: string | null;
      _schemaDelimiter: string;
    };
    _previousDataValues: FileUploadDataValues;
  };
  message: string;
};
