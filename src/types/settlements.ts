import { CurrencyType } from './utils';

export type SettlementType = {
  id: number;
  account_id: number;
  reference: string;
  type: number;
  category: number;
  payment_method: 'bank_transfer' | 'wallet' | 'card' | 'pay_with_bank' | 'mobile_money' | 'bank_account';
  amount: number;
  fee: number;
  vat: number;
  transactions_amount: number;
  currency: CurrencyType;
  payment_sources_count: number;
  is_approved: number;
  is_processed: number;
  comment: number | null;
  expected_settlement_date: string;
  source: number;
  createdAt: number;
  updatedAt: number;
  amount_settled: number;
  processed_at?: number;
  settlement_payout: {
    fee: number | null;
    vat: number | null;
  } | null;
  rolling_reserve: {
    amount: number | null;
    reserved_at: string;
  } | null;
  sentinal_settlement: {
    total_processing_fee: number | null;
    total_vat: number | null;
  };
  account: {
    name: string;
  };
};
