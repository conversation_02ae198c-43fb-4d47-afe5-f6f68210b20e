import { CheckboxType } from '+types';

type queriesParamType =
  | 'current'
  | 'limit'
  | 'status'
  | 'page'
  | 'general'
  | 'compliance'
  | 'kycTab'
  | 'tab'
  | 'verified'
  | 'kyc'
  | 'feedback'
  | 'ready'
  | 'signup'
  | 'rejected'
  | 'submitted'
  | 'sorterType'
  | 'subTab'
  | 'activeCurrency'
  | 'dateFrom'
  | 'dateTo'
  | 'keyword'
  | 'selectedConfig'
  | 'activeTab'
  | 'amountRange'
  | 'dateCreatedTo'
  | 'dateCreatedFrom'
  | 'selectData'
  | 'amountSubfilter'
  | 'product'
  | 'totalItems'
  | 'payment-preferences'
  | 'currency'
  | 'currencyAccessStatus'
  | 'previousLimit';
// eslint-disable-next-line import/prefer-default-export

export const queriesParams: { [k in queriesParamType]: queriesParamType } = {
  current: 'current',
  limit: 'limit',
  status: 'status',
  page: 'page',
  general: 'general',
  compliance: 'compliance',
  kycTab: 'kycTab',
  tab: 'tab',
  verified: 'verified',
  kyc: 'kyc',
  feedback: 'feedback',
  ready: 'ready',
  signup: 'signup',
  rejected: 'rejected',
  submitted: 'submitted',
  sorterType: 'sorterType',
  subTab: 'subTab',
  activeCurrency: 'activeCurrency',
  currency: 'currency',
  dateFrom: 'dateFrom',
  dateTo: 'dateTo',
  keyword: 'keyword',
  selectedConfig: 'selectedConfig',
  activeTab: 'activeTab',
  amountRange: 'amountRange',
  dateCreatedTo: 'dateCreatedTo',
  dateCreatedFrom: 'dateCreatedFrom',
  selectData: 'selectData',
  amountSubfilter: 'amountSubfilter',
  product: 'product',
  totalItems: 'totalItems',
  'payment-preferences': 'payment-preferences',
  currencyAccessStatus: 'currencyAccessStatus',
  previousLimit: 'previousLimit'
};

export const eventType = {
  kyc: 'Individual (KYC)',
  kyb: 'Business (KYB)'
};

export const statusIcon = {
  failed: {
    icon: 'invalid',
    color: 'red'
  },
  invalid: {
    icon: 'invalid',
    color: 'red'
  },
  valid: {
    icon: 'check',
    color: 'green'
  },
  undefined: {
    icon: 'unknown',
    color: undefined
  }
};

export const regions = {
  ng: 'Nigeria',
  ke: 'Kenya',
  gh: 'Ghana',
  za: 'South Africa'
};

type storageDataKeyType = 'AVAILABLE_CURRENCIES' | 'SINGLE_MERCHANT';

export const storageDataKey: { [k in storageDataKeyType]: storageDataKeyType } = {
  AVAILABLE_CURRENCIES: 'AVAILABLE_CURRENCIES',
  SINGLE_MERCHANT: 'SINGLE_MERCHANT'
};

export const timeMode = {
  startTime: 'startTime',
  endTime: 'endTime'
};
export const hourData: string[] = ['12', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11'];
export const minuteData: string[] = [
  '00',
  '01',
  '02',
  '03',
  '04',
  '05',
  '06',
  '07',
  '08',
  '09',
  '10',
  '11',
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
  '18',
  '19',
  '20',
  '21',
  '22',
  '23',
  '24',
  '25',
  '26',
  '27',
  '28',
  '29',
  '30',
  '31',
  '32',
  '33',
  '34',
  '35',
  '36',
  '37',
  '38',
  '39',
  '40',
  '41',
  '42',
  '43',
  '44',
  '45',
  '46',
  '47',
  '48',
  '49',
  '50',
  '51',
  '52',
  '53',
  '54',
  '55',
  '56',
  '57',
  '58',
  '59'
];

export const cardStatus = {
  pre_authorized: 'Pre-Authorized',
  void_authorization: 'Voided (Auth)',
  void_capture: 'Voided (Capture)'
};

export const currencyOrder: string[] = ['NGN', 'USD', 'GBP', 'EUR', 'GHS', 'KES', 'ZAR', 'XAF', 'XOF', 'EGP'];

export const identityCountries: CheckboxType[] = [
  { label: 'Nigeria (NG)', value: 'ng' },
  { label: 'Ghana (GH)', value: 'gh' },
  { label: 'Kenya (KE)', value: 'ke' },
  { label: 'South Africa (ZA)', value: 'za' }
];

export const identityCountriesWithoutCode: CheckboxType[] = [
  { label: 'Nigeria', value: 'ng' },
  { label: 'Ghana', value: 'gh' },
  { label: 'Kenya', value: 'ke' },
  { label: 'South Africa', value: 'za' }
];

export const kycTitles = {
  ng_passport: 'International Passport',
  ng_bvn: 'Bank Verification Number (BVN)',
  ng_nin: 'National Identity Number (NIN)',
  ng_vnin: 'Virtual NIN',
  ng_nin_phone: 'NIN Phone',
  ng_pvc: 'Voter’s Card',
  ng_phone: 'Phone Number',
  ng_tin: 'Taxpayer Identification Number',
  ng_national_id: 'National ID',
  gh_passport: 'International Passport',
  gh_ssnit: 'Social Security and National Insurance Trust',
  gh_voters_card: "Voter's Card",
  gh_drivers_license: 'Driver’s License',
  ke_passport: 'International Passport',
  ke_national_id: 'National ID',
  ke_tax_pin: 'Tax Pin',
  ke_phone: 'Phone Number',
  ke_get_phone: 'Get Phone',
  za_said: 'South African Identity Document',
  selfie_validation: 'Surported ID'
};

export const kybTitles = {
  ng_cac: 'Registration Number / CAC'
};
