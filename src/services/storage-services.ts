import { decryptContent, encryptContent, logError } from '+utils';

export function IsJsonString(str: string): boolean {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
}

export class Storage {
  static getItem(item: string) {
    const value: string | null = localStorage.getItem(item);
    if (value === undefined || value === null) {
      return null;
    }
    try {
      const decryptedValue: string = decryptContent(value);
      const parsedValue: string = IsJsonString(decryptedValue) ? JSON.parse(decryptedValue) : decryptedValue;
      return parsedValue;
    } catch (error) {
      logError(error);
    }
    return value;
  }

  static setItem(item: string, itemValue: any) {
    try {
      let value: string | null = itemValue;
      if (typeof itemValue === 'object' && itemValue !== null) {
        value = JSON.stringify(itemValue);
      }
      localStorage.setItem(item, encryptContent(value));
    } catch (error) {
      logError(error);
    }
  }

  static removeItem(item: string): void {
    try {
      localStorage.removeItem(item);
    } catch (error) {
      logError(error);
    }
  }

  static checkAuthentication() {
    let userToken;
    try {
      userToken = Storage.getItem('koraAdminUserToken');
    } catch (error) {
      logError(error);
      return false;
    }
    return userToken;
  }

  static clientToken() {
    let clientToken;
    try {
      clientToken = Storage.getItem('koraAdminClientToken');
    } catch (error) {
      logError(error);
      return false;
    }
    return clientToken;
  }

  static getRefreshToken() {
    let refreshToken;
    try {
      refreshToken = Storage.getItem('koraAdminRefreshToken');
    } catch (error) {
      logError(error);
      return false;
    }
    return refreshToken;
  }

  static checkExpiration() {
    let userTokenExpiration;
    try {
      userTokenExpiration = Storage.getItem('koraAdminUserTokenExpiration');
    } catch (error) {
      logError(error);
      return null;
    }
    return userTokenExpiration || null;
  }
}
