import { Storage } from '+services/storage-services';

import { authConstants } from '../constants';

// Set the initial state of the user
export const INITIAL_STATE = {
  clientToken: Storage.clientToken(),
  userToken: Storage.checkAuthentication(),
  refreshToken: Storage.getRefreshToken(),
  userTokenExpiration: Storage.checkExpiration(),
  isAuthenticated: !!Storage.checkAuthentication(),
  isAuthorized: false,
  authorizeData: {},
  isLoading: false,
  error: false,
  profile: Storage.getItem('koraAdminUserProfile') || {
    email: '',
    avatar: ''
  },
  errors: {}
};

export function account(state = INITIAL_STATE, action) {
  switch (action.type) {
    case authConstants.OAUTH_AUTHORIZE_REQUEST:
      return {
        ...state,
        userToken: null,
        refreshToken: null,
        isLoading: true,
        error: false,
        isAuthorized: false,
        isAuthenticated: false,
        errors: {}
      };
    case authConstants.OAUTH_AUTHORIZE_FAILURE:
      return {
        ...state,
        isLoading: false,
        userToken: null,
        refreshToken: null,
        error: true,
        isAuthorized: false,
        errors: action.errors.response.data
      };
    case authConstants.OAUTH_AUTHORIZE_SUCCESS:
      return {
        ...state,
        isLoading: false,
        userToken: null,
        refreshToken: null,
        error: false,
        isAuthorized: true,
        authorizeData: action.user.data
      };
    case authConstants.OAUTH_COMPLETE_REQUEST:
      return {
        ...state,
        userToken: null,
        refreshToken: null,
        isLoading: true,
        error: false,
        isAuthorized: false,
        errors: {}
      };
    case authConstants.OAUTH_COMPLETE_FAILURE:
      return {
        ...state,
        isLoading: false,
        userToken: null,
        refreshToken: null,
        error: true,
        isAuthenticated: false,
        isAuthorized: false,
        errors: action.errors.response?.data
      };
    case authConstants.OAUTH_COMPLETE_SUCCESS:
      return {
        ...state,
        isAuthorized: true,
        userToken: action.user.data.access_token,
        refreshToken: action.user.data.refresh_token,
        userTokenExpiration: action.user.data.expires_in,
        isAuthenticated: true,
        isLoading: false,
        error: false,
        profile: {
          email: action.user.data.account.email,
          avatar: action.user.data.account.avatar
        },
        errors: {}
      };
    case authConstants.REFRESH_TOKEN:
      return {
        ...state,
        userToken: action.token.access_token,
        refreshToken: action.token.refresh_token,
        userTokenExpiration: action.token.expires_in
      };

    case authConstants.LOGOUT:
      return {
        ...state,
        isLoading: false,
        userToken: null,
        refreshToken: null,
        isAuthenticated: false,
        error: false,
        isAuthorized: false,
        profile: {},
        userTokenExpiration: null,
        errors: {}
      };
    default:
      return state;
  }
}
