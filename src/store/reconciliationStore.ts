import { create } from 'zustand';
import { combine, createJSONStorage, devtools, persist } from 'zustand/middleware';

import { ComparisonKeyMappingType, IProcessorReportMapping, PrimaryKeyMappingType, ReconciliationDataType } from '+types';
import { StoreStorage } from '+utils';

interface IStartReconciliationState {
  startReconciliationData: ReconciliationDataType;
  primaryKeyMappings: PrimaryKeyMappingType;
  comparisonKeyMappings: ComparisonKeyMappingType;
}

interface IStartReconciliationAction {
  setStartReconciliationData: (data: ReconciliationDataType) => void;
  setPrimaryKeyMappings: (data: PrimaryKeyMappingType) => void;
  setComparisonKeyMappings: (data: ComparisonKeyMappingType) => void;
  updateComparisonKeyMappings: (data: { id: string; value: Partial<IProcessorReportMapping> }) => void;
  deleteComparisonKeyMapping: (id: string) => void;
  clearPrimaryKeyMappings: () => void;
  clearComparisonKeyMappings: () => void;
}
const initialState = {
  startReconciliationData: {} as ReconciliationDataType,
  primaryKeyMappings: [] as PrimaryKeyMappingType,
  comparisonKeyMappings: [] as ComparisonKeyMappingType
};

const useReconciliationStore = create(
  devtools(
    persist(
      combine<IStartReconciliationState, IStartReconciliationAction>(initialState, set => ({
        startReconciliationData: {},
        setStartReconciliationData: (data: ReconciliationDataType) => {
          set(state => ({
            ...state,
            startReconciliationData: {
              ...state.startReconciliationData,
              ...data
            }
          }));
        },
        setPrimaryKeyMappings: (data: PrimaryKeyMappingType) => {
          set(state => ({
            ...state,
            primaryKeyMappings: [...state.primaryKeyMappings, ...data]
          }));
        },
        setComparisonKeyMappings: (data: ComparisonKeyMappingType) => {
          set(state => ({
            ...state,
            comparisonKeyMappings: [...state.comparisonKeyMappings, ...data]
          }));
        },
        updateComparisonKeyMappings: (data: { id: string; value: Partial<IProcessorReportMapping> }) => {
          set(state => ({
            ...state,
            comparisonKeyMappings: state.comparisonKeyMappings.map(mapping => {
              const updatedMapping = data.id === mapping.id ? { ...mapping, ...data.value } : mapping;
              return updatedMapping;
            })
          }));
        },
        deleteComparisonKeyMapping: (id: string) => {
          set(state => ({
            ...state,
            comparisonKeyMappings: state.comparisonKeyMappings.filter(mapping => mapping.id !== id)
          }));
        },
        clearPrimaryKeyMappings: () => set(state => ({ ...state, primaryKeyMappings: [] })),
        clearComparisonKeyMappings: () => set(state => ({ ...state, comparisonKeyMappings: [] }))
      })),
      {
        name: 'reconciliation',
        storage: createJSONStorage(() => StoreStorage)
      }
    )
  )
);

export default useReconciliationStore;
