@import '../../../../styles/kpy-custom/_custom.scss';
@import '../../../../styles/kpy-custom/variables.scss';

.user-new-invite {
  .element-input-box {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    @media (min-width: $breakpoint-desktop) {
      flex-direction: row;
    }

    .input-info {
      flex-grow: 1;
      max-width: 21.98rem;

      p {
        color: #3e4b5b;
        font-size: 0.9rem;
        font-weight: 600;
        line-height: 1.09rem;
        margin-bottom: 0.38rem;
      }

      ::placeholder {
        color: #a9afbc;
      }
    }
  }

  .element-link-exclament {
    display: flex;
    align-items: center;
    margin-top: 2.04rem;
    margin-bottom: 2.04rem;
    gap: 1rem;

    @media (max-width: $breakpoint-tablet) {
      flex-direction: column;
      align-items: flex-start !important;
    }

    .btn-user-permission {
      color: #2376f3;
      font-size: 0.9rem;
      padding: 0;
      display: block;
      width: fit-content;
    }

    .element-exclamen-cont {
      gap: 0.1rem !important;
    }

    .element-exclament {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 0.5rem;
      padding: 0;

      .img-exclament-mark {
        width: 0.78rem;
        height: 0.77rem;
        line-height: 0;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .legend-dash {
    height: 0.047rem;
    background-color: #d8d8d8;
    margin-bottom: 1.5rem;
  }

  .element-permission-modify {
    .element-permission-box {
      margin-bottom: 1.5rem;

      .permission-modify-box {
        margin-bottom: 1.5rem;
        font-weight: 600;
        font-size: 0.99rem;
        line-height: 26px;
        letter-spacing: -0.5px;
        color: #4e555b;
      }

      .permission-modify-cont {
        border-radius: 10px;
        background-color: #fbfcfd;
        border: 1px solid #d3e4fd;
        padding: 0;

        .permission-modify-roles {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 20px;
          border-bottom: 1px solid #d3e4fd;
          margin: auto;

          p {
            margin: initial;
          }

          p:first-of-type {
            font-weight: 500;
            font-size: 0.9rem;
            line-height: 23px;
            letter-spacing: -0.003em;
            color: #414f5f;
          }

          p:last-child {
            font-style: italic;
            color: #9fa6ae;
            font-weight: 300;
            margin-bottom: 0;
          }
        }
        .permission-modify-roles:last-child {
          border-bottom: none;
        }
      }
    }
  }

  button {
    &.btn-save-role {
      background-color: #2376f3;
      color: #ffffff;
      font-weight: 600;
      font-size: 0.99rem;

      &:hover {
        color: #ffffff;
      }
    }
  }

  .role-permission-actions {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 0.5rem;
  }
}

.permission-modify-cont {
  border-radius: 10px;
  background-color: #fbfcfd;
  padding: 8px 20px;

  .permission-modify-roles {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #3e4b5b1a;
    margin: auto;
    margin-left: 0;
    padding: 0.85rem 0;

    p {
      margin: initial;
    }

    p:first-of-type {
      font-weight: 500;
      font-size: 0.9rem;
      line-height: 23px;
      letter-spacing: -0.003em;
      color: #3e4b5b;
    }
  }
  .permission-modify-roles:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
}

.back-to-roles {
  font-weight: 600;
}

.os-rolesummary-tabs {
  display: flex;
  flex-direction: column;

  .os-rolesummary {
    padding: 1.4rem 2rem;
  }

  .os-rolesummary-box {
    background-color: #f9fbfd;
    border-top: solid 1px #0000000d;
    border-bottom: solid 1px #0000000d;
  }

  .sec-details-hr {
    display: none;
  }
}

.unable-to-delete {
  background: #fff8e1;
  border-radius: 5px;
  padding: 1rem 1.25rem;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.cd-maintitle-btn {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: 0.7rem;

  .cd-btns {
    display: flex;
    align-items: flex-start;

    .cd-btn-divider {
      border-left: 1px solid #dde2ec;
      margin: 0 0.7rem;
      height: 1.5rem;
    }

    .delete-role {
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #f32345;
    }
  }

  button {
    margin-bottom: 0;
    font-size: 0.8rem;
    font-weight: 500;
    color: #2376f3;
    background-color: transparent;
    border: none;
    width: fit-content;

    span {
      svg {
        width: 1.3rem;
        padding-right: 7px;
      }
    }
  }
}

.div-table-assignee {
  .more-option {
    display: flex !important;
    color: #2376f3;
    cursor: pointer;
    font-weight: 500;
    align-items: center;
  }

  .option-divider {
    height: 10px;
    border-left: 0.5px solid #2376f3;
    margin: 0 10px;
    display: block;
  }
}

.div-table-tablehead {
  @media only screen and (min-width: $breakpoint-desktop) {
    > div {
      width: 15%;

      &:first-child {
        width: 20%;
      }

      &:nth-of-type(2) {
        width: 50%;
      }
    }
  }
}

.div-table-tablebody {
  > .--users-table {
    @media only screen and (min-width: $breakpoint-desktop) {
      > div {
        width: 15%;

        &:first-child {
          width: 20%;

          .no-ellipsis {
            text-overflow: inherit !important;
            white-space: normal !important;
          }
        }

        &:nth-of-type(2) {
          width: 50%;
        }
      }
    }
  }
}

.div-table.--assignee-table {
  @media only screen and (min-width: $breakpoint-desktop) {
    > div {
      width: 20%;
      white-space: normal !important;

      &:first-child {
        width: 25%;

        .no-ellipsis {
          text-overflow: inherit !important;
          white-space: normal !important;
        }
      }

      &:nth-of-type(2) {
        width: 50%;
      }
    }
  }
}
