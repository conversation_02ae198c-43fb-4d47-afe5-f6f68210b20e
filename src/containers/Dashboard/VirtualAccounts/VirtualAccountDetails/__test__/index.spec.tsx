import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';

import { mockedFvbaNumberDetails, mockedUSDVbaAccountNumberDetails } from '+mock/mockData';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';
import { server } from '+mock/mockServers';

import { TBankAccountDetails } from '../../data';
import AccountNumberDetails from '../index';

const MockedAccountNumberDetails = () => {
  return (
    <MockIndexWithRoute route="/:id" initialEntries={['/test']}>
      <AccountNumberDetails />
    </MockIndexWithRoute>
  );
};

describe('AccountNumberDetails', () => {
  test('AccountNumberDetails is accessible', async () => {
    const { container } = render(<MockedAccountNumberDetails />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('render go back button', async () => {
    render(<MockedAccountNumberDetails />);
    expect(await screen.findByText(/go back/i)).toBeInTheDocument();
  });

  describe('Ngn Account Number Details Test Cases', () => {
    test('render the account number details information on the ui', async () => {
      render(<MockedAccountNumberDetails />);

      expect(await screen.findByText('(0) Transactions')).toBeInTheDocument();
      expect(((await screen.findAllByText(/account number/i)) as unknown as HTMLElement[])?.[0]).toBeInTheDocument();
      expect(await screen.findAllByText('**********')).toHaveLength(2);
      expect(screen.getByText(/account name/i)).toBeInTheDocument();
      expect(screen.getByText(/Steph James/i)).toBeInTheDocument();
      expect(screen.getByText(/account reference/i)).toBeInTheDocument();
      expect(screen.getByText('KPY-VA-YnTjb6GsGfLgqvh')).toBeInTheDocument();
      expect(screen.getByText(/bank name/i)).toBeInTheDocument();
      expect(screen.getByText(/account status/i)).toBeInTheDocument();
      expect(screen.getByText(/currency/i)).toBeInTheDocument();
      expect(screen.getByText(/email/i)).toBeInTheDocument();
      expect(screen.getByText(/date created/i)).toBeInTheDocument();
    });

    test('Foreign account number details information should not render in the NGN account details ', async () => {
      render(<MockedAccountNumberDetails />);

      expect(screen.queryByText('**********************')).not.toBeInTheDocument();
      expect(screen.queryByText(/tier/i)).not.toBeInTheDocument();
      expect(screen.queryByText('Max. Funding Limit (Per Transaction)')).not.toBeInTheDocument();
      expect(screen.queryByText('Max. Funding Limit (Daily)')).not.toBeInTheDocument();
      expect(screen.queryByText('Max. Funding Limit (Monthly)')).not.toBeInTheDocument();
    });

    test('Account number transactions(2) should be rendered in the ui', async () => {
      render(<MockedAccountNumberDetails />);
      expect(await screen.findByText('(0) Transactions')).toBeInTheDocument();
      expect(await screen.findByText('Account Number')).toBeInTheDocument();

      expect(await screen.findByText('Account Name')).toBeInTheDocument();
      expect(await screen.findByText('Steph James')).toBeInTheDocument();
      expect(await screen.findAllByText('**********')).toHaveLength(2);

      expect(await screen.findByText('KPY-PAY-NNXocYX80DluEPm')).toBeInTheDocument();
      expect(await screen.findByText('KPY-PAY-e8A3KCWhVJu9q9A')).toBeInTheDocument();
    });
  });

  describe('Foreign Account Number Details Test Cases', () => {
    beforeEach(() => {
      server.use(
        http.get('/admin/virtual-bank-account/test', () => {
          return HttpResponse.json(mockedFvbaNumberDetails, { status: 200 });
        })
      );
    });

    test('render the account number details information on the ui', async () => {
      render(<MockedAccountNumberDetails />);

      expect(await screen.findAllByText(/account number/i)).toHaveLength(2);
      expect(screen.getAllByText('**********')).toHaveLength(2);
      expect(screen.getByText(/account name/i)).toBeInTheDocument();
      expect(screen.getByText(/Steph James/i)).toBeInTheDocument();
      expect(screen.getByText(/account type/i)).toBeInTheDocument();
      expect(screen.getByText(/individual/i)).toBeInTheDocument();
      expect(screen.getByText(/bank name/i)).toBeInTheDocument();
      expect(screen.getByText(/account status/i)).toBeInTheDocument();
      expect(screen.getByText(/currency/i)).toBeInTheDocument();
      expect(screen.getByText(/email/i)).toBeInTheDocument();
      expect(screen.getByText(/date created/i)).toBeInTheDocument();
      expect(screen.getAllByText(/tier 1/i)).toHaveLength(2);
      expect(screen.getByText('Max. Funding Limit (Per Transaction)')).toBeInTheDocument();
      expect(screen.getByText('Max. Funding Limit (Daily)')).toBeInTheDocument();
      expect(screen.getByText('Max. Funding Limit (Monthly)')).toBeInTheDocument();
    });

    test('Manage account holder should not be rendered in the ui', async () => {
      render(<MockedAccountNumberDetails />);

      expect(await screen.findByText(/manage account number/i)).toBeInTheDocument();
    });

    test('Account number transactions(2) should be rendered in the ui', async () => {
      render(<MockedAccountNumberDetails />);

      expect(await screen.findByText('KPY-PAY-NNXocYX80DluEPm')).toBeInTheDocument();
      expect(await screen.findByText('KPY-PAY-e8A3KCWhVJu9q9A')).toBeInTheDocument();
    });

    test('approved account number option should render the suspend account option', async () => {
      render(<MockedAccountNumberDetails />);

      const actionBtn = await screen.findByText(/manage account number/i);
      await userEvent.click(actionBtn);

      await screen.findByText(/suspend account number/i);
    });

    describe('Manage Foreign account number tests', () => {
      test('Suspend modal should not render the tell us why input field unless the user chooses the "other" option', async () => {
        render(<MockedAccountNumberDetails />);

        const actionBtn = await screen.findByText(/manage account number/i);
        await userEvent.click(actionBtn);
        await screen.findByText(/suspend account number/i);

        const suspendBtn = screen.getByText(/suspend account number/i);
        await userEvent.click(suspendBtn);

        expect(screen.queryByTestId('reason-text')).not.toBeInTheDocument();
      });

      test('Suspend modal should render the tell us why input field when user chooses the "other" option', async () => {
        render(<MockedAccountNumberDetails />);

        const actionBtn = await screen.findByText(/manage account number/i);
        await userEvent.click(actionBtn);
        const suspendBtn = await screen.findByText(/suspend account number/i);

        await userEvent.click(suspendBtn);
        expect(await screen.findByTestId('second-button')).toBeDisabled();

        const selectReasonDropdown = screen.getByTestId('select-reason');
        await userEvent.selectOptions(selectReasonDropdown, 'other');
        expect(await screen.findByTestId('reason-text')).toBeInTheDocument();
      });

      test('test suspend account number flow', async () => {
        render(<MockedAccountNumberDetails />);

        const actionBtn = await screen.findByText(/manage account number/i);
        await userEvent.click(actionBtn);

        const suspendBtn = await screen.findByText(/suspend account number/i);
        await userEvent.click(suspendBtn);

        const selectReasonDropdown = await screen.findByTestId('select-reason');
        await userEvent.selectOptions(selectReasonDropdown, 'suspected_fraud');
        await waitFor(() => expect((selectReasonDropdown as unknown as HTMLSelectElement).value).toBe('suspected_fraud'));

        const secondBtn = await screen.findByTestId('second-button');
        expect(secondBtn).toBeEnabled();

        await userEvent.click(screen.getByTestId('second-button'));
        expect(await screen.findByText(/yes, suspend/i)).toBeInTheDocument();

        await userEvent.click(screen.getByTestId('second-button'));
        expect(await screen.findByText(/dismiss/i)).toBeInTheDocument();
      });

      test('Deactivate modal should render only the tell us why field', async () => {
        render(<MockedAccountNumberDetails />);

        const actionBtn = await screen.findByText(/manage account number/i);
        await userEvent.click(actionBtn);
        await screen.findByText(/deactivate account number/i);

        const deactivateBtn = screen.getByText(/deactivate account number/i);
        await userEvent.click(deactivateBtn);
        await waitFor(() => expect(screen.getByTestId('second-button')).toBeDisabled());

        expect(screen.queryByTestId('select-reason')).not.toBeInTheDocument();
        expect(screen.getByTestId('reason-text')).toBeInTheDocument();
      });

      test('test deactivate account flow', async () => {
        render(<MockedAccountNumberDetails />);

        const actionBtn = await screen.findByText(/manage account number/i);
        await userEvent.click(actionBtn);

        const deactivateBtn = await screen.findByText(/deactivate account number/i);
        await userEvent.click(deactivateBtn);
        await waitFor(() => expect(screen.getByTestId('second-button')).toBeDisabled());

        await userEvent.type(screen.getByTestId('reason-text'), 'User is fraudulent');
        expect(await screen.findByTestId('second-button')).toBeEnabled();

        await userEvent.click(screen.getByTestId('second-button'));
        expect(await screen.findByText(/yes, deactivate/i)).toBeInTheDocument();

        await userEvent.click(screen.getByTestId('second-button'));
        expect(await screen.findByText(/dismiss/i)).toBeInTheDocument();
      });

      test('suspend account number option should not render if account has already been suspended', async () => {
        mockedFvbaNumberDetails.data.account_status = 'suspended';

        render(<MockedAccountNumberDetails />);

        const actionBtn = await screen.findByText(/manage account number/i);
        await userEvent.click(actionBtn);

        await waitFor(() => expect(screen.queryByText(/suspend account number/i)).not.toBeInTheDocument());
      });

      test('suspend account number option should show the reason why an account is suspended and close on dismiss clicked', async () => {
        const reasonText = 'user is fraudulent';
        mockedFvbaNumberDetails.data.account_status = 'suspended';
        (mockedFvbaNumberDetails.data as unknown as TBankAccountDetails).status_reason = reasonText;

        render(<MockedAccountNumberDetails />);

        const reasonBtn = await screen.findByText('Learn Why');
        await userEvent.click(reasonBtn);

        expect(await screen.findByText(reasonText)).toBeInTheDocument();

        const dismissBtn = await screen.findByText(/dismiss/i);
        await userEvent.click(dismissBtn);

        expect(screen.queryByText(reasonText)).not.toBeInTheDocument();
      });

      test('success modal does not render if request is not successful', async () => {
        mockedFvbaNumberDetails.data.account_status = 'active';

        server.use(
          http.post('/admin/virtual-bank-account/manage', () => {
            return HttpResponse.json({}, { status: 400 });
          })
        );

        render(<MockedAccountNumberDetails />);

        const actionBtn = await screen.findByText(/manage account number/i);
        await userEvent.click(actionBtn);
        await screen.findByText(/deactivate account number/i);

        const deactivateBtn = screen.getByText(/deactivate account number/i);
        await userEvent.click(deactivateBtn);
        expect(await screen.findByTestId('second-button')).toBeDisabled();

        await userEvent.type(screen.getByTestId('reason-text'), 'User is fraudulent');
        expect(await screen.findByTestId('second-button')).toBeEnabled();

        await userEvent.click(screen.getByTestId('second-button'));
        expect(await screen.findByText(/yes, deactivate/i)).toBeInTheDocument();

        await userEvent.click(screen.getByTestId('second-button'));
        expect(screen.getByText(/yes, deactivate/i)).toBeInTheDocument();
      });
    });

    describe('USD test cases', () => {
      beforeEach(() => {
        server.use(
          http.get('/admin/virtual-bank-account/test', () => {
            return HttpResponse.json(mockedUSDVbaAccountNumberDetails, { status: 200 });
          })
        );
      });

      test('USD account number primary details should be in the ui when component renders', async () => {
        render(<MockedAccountNumberDetails />);
        expect(await screen.findByText(/ACH Routing code/i)).toBeInTheDocument();
        expect(await screen.findByText(/ACH memo/i)).toBeInTheDocument();
        expect(await screen.findByText(/swift code/i)).toBeInTheDocument();
        expect(await screen.findByText(/swift memo/i)).toBeInTheDocument();
        expect(await screen.findByText(/fedwire routing code/i)).toBeInTheDocument();
      });

      test('USD account number hidden details should NOT be in the ui when component renders', async () => {
        render(<MockedAccountNumberDetails />);

        expect(screen.queryByText(/fedwire memo/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/account status/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/account type/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/account tier/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/currency/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/email/i)).not.toBeInTheDocument();
        expect(screen.queryByText('Max. Funding Limit (Per Transaction)')).not.toBeInTheDocument();
        expect(screen.queryByText('Max. Funding Limit (Daily)')).not.toBeInTheDocument();
        expect(screen.queryByText('Max. Funding Limit (Monthly)')).not.toBeInTheDocument();
      });

      test('USD account number hidden details should render when "show more details btn" is clicked and hidden when "hide details btn is clicked"', async () => {
        render(<MockedAccountNumberDetails />);
        await userEvent.click(await screen.findByText(/see more details/i));

        expect(screen.getByText(/fedwire memo/i)).toBeInTheDocument();
        expect(screen.getByText(/account status/i)).toBeInTheDocument();
        expect(screen.getByText(/account type/i)).toBeInTheDocument();
        expect(screen.getByText(/account tier/i)).toBeInTheDocument();
        expect(screen.getByText(/currency/i)).toBeInTheDocument();
        expect(screen.getByText(/email/i)).toBeInTheDocument();
        expect(screen.getByText('Max. Funding Limit (Per Transaction)')).toBeInTheDocument();
        expect(screen.getByText('Max. Funding Limit (Daily)')).toBeInTheDocument();
        expect(screen.getByText('Max. Funding Limit (Monthly)')).toBeInTheDocument();

        await userEvent.click(await screen.findByText(/hide details/i));

        expect(screen.queryByText(/fedwire memo/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/account status/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/account type/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/account tier/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/currency/i)).not.toBeInTheDocument();
        expect(screen.queryByText(/email/i)).not.toBeInTheDocument();
        expect(screen.queryByText('Max. Funding Limit (Per Transaction)')).not.toBeInTheDocument();
        expect(screen.queryByText('Max. Funding Limit (Daily)')).not.toBeInTheDocument();
        expect(screen.queryByText('Max. Funding Limit (Monthly)')).not.toBeInTheDocument();
      });
    });
  });

  test('a deactivated account should not have the manage account number options when viewed', async () => {
    mockedFvbaNumberDetails.data.account_status = 'deactivated';

    render(<MockedAccountNumberDetails />);
    expect(screen.queryByText(/manage account number/i)).not.toBeInTheDocument();
  });
});
