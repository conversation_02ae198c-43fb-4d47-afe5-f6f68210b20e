import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import { mockedVbaUpgradeReq } from '+mock/mockData';
import MockIndex from '+mock/MockIndex';

import { TUpgradeRequestDetails } from '../../data';
import UpgradeRequestDetails from '../index';

const mockedDeclinedUpgradeReq = { ...mockedVbaUpgradeReq.data, status: 'declined' } as unknown as TUpgradeRequestDetails;
const mockedApprovedeUpgradeReq = { ...mockedVbaUpgradeReq.data, status: 'approved' } as unknown as TUpgradeRequestDetails;

const MockedUpgradeRequestDetails = ({ data, updateModalState }: { data: TUpgradeRequestDetails; updateModalState: () => void }) => {
  return (
    <MockIndex>
      <UpgradeRequestDetails data={data || mockedVbaUpgradeReq.data} updateModalState={updateModalState} />
    </MockIndex>
  );
};

describe('UpgradeRequestDetails Tests', () => {
  test('UpgradeRequestDetails is accessible', async () => {
    const { container } = render(<MockedUpgradeRequestDetails data={mockedApprovedeUpgradeReq} updateModalState={vi.fn()} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Approved request should not render action buttons in the modal', async () => {
    render(<MockedUpgradeRequestDetails data={mockedApprovedeUpgradeReq} updateModalState={vi.fn()} />);

    expect(screen.queryByTestId('first-button')).not.toBeInTheDocument();
    expect(screen.queryByTestId('second-button')).not.toBeInTheDocument();
  });

  test('Declined request should not render action buttons in the modal', async () => {
    render(<MockedUpgradeRequestDetails data={mockedDeclinedUpgradeReq} updateModalState={vi.fn()} />);

    expect(screen.queryByTestId('first-button')).not.toBeInTheDocument();
    expect(screen.queryByTestId('second-button')).not.toBeInTheDocument();
  });

  test('Pending request should render action buttons in the modal', async () => {
    render(<MockedUpgradeRequestDetails data={mockedVbaUpgradeReq.data as unknown as TUpgradeRequestDetails} updateModalState={vi.fn()} />);

    expect(await screen.findByTestId('first-button')).toBeInTheDocument();
    expect(await screen.findByTestId('second-button')).toBeInTheDocument();
  });

  test('Pending request should be approved', async () => {
    render(<MockedUpgradeRequestDetails data={mockedVbaUpgradeReq.data as unknown as TUpgradeRequestDetails} updateModalState={vi.fn()} />);

    await userEvent.click(await screen.findByTestId('second-button'));
    expect(await screen.findByText('Yes, Approve')).toBeInTheDocument();

    await userEvent.click(await screen.findByTestId('second-button'));
    expect(await screen.findByText('Dismiss')).toBeInTheDocument();
  });

  test('Confirm decline pending request button should be disabled if reason is not provided', async () => {
    render(<MockedUpgradeRequestDetails data={mockedVbaUpgradeReq.data as unknown as TUpgradeRequestDetails} updateModalState={vi.fn()} />);

    await userEvent.click(await screen.findByTestId('first-button'));
    expect(await screen.findByText('Yes, Decline')).toBeDisabled();

    await userEvent.click(await screen.findByTestId('second-button'));
  });

  test('Confirm decline pending request button should be enabled if reason is provided', async () => {
    render(<MockedUpgradeRequestDetails data={mockedVbaUpgradeReq.data as unknown as TUpgradeRequestDetails} updateModalState={vi.fn()} />);

    await userEvent.click(await screen.findByTestId('first-button'));

    const reasonInput = await screen.findByTestId('reason-text');
    await userEvent.type(reasonInput, 'document not valid');
    expect(await screen.findByText('Yes, Decline')).toBeEnabled();
  });

  test('Pending request should be declined', async () => {
    render(<MockedUpgradeRequestDetails data={mockedVbaUpgradeReq.data as unknown as TUpgradeRequestDetails} updateModalState={vi.fn()} />);

    await userEvent.click(await screen.findByTestId('first-button'));

    const reasonInput = await screen.findByTestId('reason-text');
    await userEvent.type(reasonInput, 'document not valid');

    await userEvent.click(await screen.findByTestId('second-button'));
    expect(await screen.findByText('Dismiss')).toBeInTheDocument();
  });

  describe('Request Details test', () => {
    test('dropdown clicks should show and hide the information', async () => {
      render(
        <MockedUpgradeRequestDetails data={mockedVbaUpgradeReq.data as unknown as TUpgradeRequestDetails} updateModalState={vi.fn()} />
      );

      expect(await screen.findByText(/request details/i)).toBeInTheDocument();
      expect(screen.queryByTestId('request-details-container')).not.toBeInTheDocument();
      const openRequestDetailsBtn = screen.getByTestId('open-request-details');

      await userEvent.click(openRequestDetailsBtn);
      expect(await screen.findByTestId('request-details-container')).toBeInTheDocument();

      await userEvent.click(openRequestDetailsBtn);
      expect(screen.queryByTestId('request-details-container')).not.toBeInTheDocument();
    });

    test('relevant information should show when dropdown is opened', async () => {
      render(
        <MockedUpgradeRequestDetails data={mockedVbaUpgradeReq.data as unknown as TUpgradeRequestDetails} updateModalState={vi.fn()} />
      );

      const openRequestDetailsBtn = await screen.findByTestId('open-request-details');
      await userEvent.click(openRequestDetailsBtn);

      expect(await screen.findByText('Account Name')).toBeInTheDocument();
      expect(screen.getByText('Testing')).toBeInTheDocument();
      expect(screen.getByText('Account Number')).toBeInTheDocument();
      expect(screen.getByText('********')).toBeInTheDocument();
      expect(screen.getByText('Bank')).toBeInTheDocument();
      expect(screen.getByText('Merchant')).toBeInTheDocument();
      expect(screen.getByText('HKT')).toBeInTheDocument();
      expect(screen.getByText('Upgrading From (Current Tier)')).toBeInTheDocument();
      expect(screen.getByText('Tier 1')).toBeInTheDocument();
      expect(screen.getByText('Upgrading To (Requested Tier)')).toBeInTheDocument();
      expect(screen.getByText('Tier 2')).toBeInTheDocument();
      expect(screen.getByText('Date Requested')).toBeInTheDocument();
      expect(screen.getByText(/6 Mar 2024 /i)).toBeInTheDocument();
      expect(screen.getByText('Reason for Upgrade')).toBeInTheDocument();
      expect(screen.getByText('Increased Sales')).toBeInTheDocument();
    });
  });

  describe('Supporting Documents test', () => {
    test('dropdown clicks should show and hide the documents', async () => {
      render(
        <MockedUpgradeRequestDetails data={mockedVbaUpgradeReq.data as unknown as TUpgradeRequestDetails} updateModalState={vi.fn()} />
      );

      expect(await screen.findByText(/request details/i)).toBeInTheDocument();
      expect(screen.queryByTestId('supporting-documents-container')).not.toBeInTheDocument();
      const openSupportingDocumentsBtn = screen.getByTestId('open-supporting-documents');

      await userEvent.click(openSupportingDocumentsBtn);
      expect(await screen.findByTestId('supporting-documents-container')).toBeInTheDocument();

      await userEvent.click(openSupportingDocumentsBtn);
      expect(screen.queryByTestId('supporting-documents-container')).not.toBeInTheDocument();
    });

    test('relevant supporting documents should be viewable when dropdown is opened', async () => {
      render(
        <MockedUpgradeRequestDetails data={mockedVbaUpgradeReq.data as unknown as TUpgradeRequestDetails} updateModalState={vi.fn()} />
      );

      const openSupportingDocumentsBtn = screen.getByTestId('open-supporting-documents');
      await userEvent.click(openSupportingDocumentsBtn);

      expect(await screen.findByText('use case for upgrade.')).toBeInTheDocument();
      expect(screen.getByText('View')).toBeInTheDocument();

      await userEvent.click(screen.getByText('View'));

      expect(await screen.findByTestId('no-modal-doc-view')).toBeInTheDocument();

      await userEvent.click(screen.getByTestId('second-button'));
      expect(screen.queryByText('View')).not.toBeInTheDocument();
    });
  });

  describe('Account Holder Information Test', () => {
    test('dropdown clicks should show and hide the information', async () => {
      render(
        <MockedUpgradeRequestDetails data={mockedVbaUpgradeReq.data as unknown as TUpgradeRequestDetails} updateModalState={vi.fn()} />
      );

      expect(await screen.findByText(/request details/i)).toBeInTheDocument();
      expect(screen.queryByTestId('account-holder-information-container')).not.toBeInTheDocument();
      const openAccountHolderBtn = screen.getByTestId('open-account-holder-information');

      await userEvent.click(openAccountHolderBtn);
      expect(await screen.findByTestId('account-holder-information-container')).toBeInTheDocument();

      await userEvent.click(openAccountHolderBtn);
      expect(screen.queryByTestId('account-holder-information-container')).not.toBeInTheDocument();
    });

    test('account holder info should show when dropdown is opened', async () => {
      render(
        <MockedUpgradeRequestDetails data={mockedVbaUpgradeReq.data as unknown as TUpgradeRequestDetails} updateModalState={vi.fn()} />
      );

      const openAccountHolderBtn = await screen.findByTestId('open-account-holder-information');
      await userEvent.click(openAccountHolderBtn);

      expect(await screen.findByText('Account Holder Name')).toBeInTheDocument();
      expect(screen.getByText('Sarah Doe')).toBeInTheDocument();
      expect(screen.getByText('Account Holder Reference')).toBeInTheDocument();
      expect(screen.getByText('KPY-AH-m0fClbD6sJr16FI')).toBeInTheDocument();
      expect(screen.getByText('Status')).toBeInTheDocument();
      expect(screen.getByText('Approved')).toBeInTheDocument();
      expect(screen.getByText('Type')).toBeInTheDocument();
      expect(screen.getByText('individual')).toBeInTheDocument();
      expect(screen.getByText('Email')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('Phone')).toBeInTheDocument();
      expect(screen.getByText('+*************')).toBeInTheDocument();
      expect(screen.getByText('Date of Birth')).toBeInTheDocument();
      expect(screen.getByText('4 Apr 1988')).toBeInTheDocument();
      expect(screen.getByText('City')).toBeInTheDocument();
      expect(screen.getByText('Lagos')).toBeInTheDocument();
      expect(screen.getByText('Country')).toBeInTheDocument();
      expect(screen.getByText('NG')).toBeInTheDocument();
    });
  });

  describe('Upgrade Request History test', () => {
    test('dropdown clicks should show and hide the history', async () => {
      render(
        <MockedUpgradeRequestDetails data={mockedVbaUpgradeReq.data as unknown as TUpgradeRequestDetails} updateModalState={vi.fn()} />
      );

      expect(await screen.findByText(/request details/i)).toBeInTheDocument();
      expect(screen.queryByTestId('upgrade-req-history-container')).not.toBeInTheDocument();
      const openRequestDetailsBtn = screen.getByTestId('open-upgrade-req-history');

      await userEvent.click(openRequestDetailsBtn);
      expect(await screen.findByTestId('upgrade-req-history-container')).toBeInTheDocument();

      await userEvent.click(openRequestDetailsBtn);
      expect(screen.queryByTestId('upgrade-req-history-container')).not.toBeInTheDocument();
    });
  });
});
