@import 'styles/kpy-custom/variables';

.upgrade-request-modal {
  padding-right: 1.25rem;
  height: 50vh;
  overflow-x: hidden;
  overflow-y: auto;

  .upgrade-request-content {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #dde2ec;
    margin-bottom: 1rem;

    &:last-child {
      border-bottom: none;
    }

    .upgrade-request-content__header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      p {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 500;
        color: #414f5f;
      }

      img {
        width: 0.6rem;
        height: 0.6rem;
      }

      .caret {
        cursor: pointer;
        transition: transform 0.3s ease-in-out;

        span {
          display: inline-block;
        }

        &.flipped {
          transform: rotate(180deg);
        }
      }
    }

    .upgrade-request-content__body {
      color: rgba(65, 79, 95, 0.7);
      margin-right: 0.75rem;
      font-size: 0.85rem;
    }

    .upgrade-request-content__body__note {
      font-size: 11px;
      background: #fff8e1;
      border-radius: 10px;
      padding: 8px 4px;
      margin-bottom: 1rem;

      img {
        width: 20px;
        margin-right: 10px;
      }
    }

    .request-details-container {
      display: flex;
      padding: 16px 20px;
      margin-bottom: 1rem;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      gap: 10px;
      align-self: stretch;
      border-radius: 10px;
      background: var(--Neutrals-neutral-200, #f1f6fa);

      .request-detail {
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
        border-bottom: 1px solid var(--Dark-dark-50, #dde2ec);
        font-size: 0.9rem;

        .holder {
          color: $kpyblue !important;
        }

        .request-detail__label {
          color: rgba(65, 79, 95, 0.4);
          margin-bottom: 0;
        }

        .request-detail__value {
          color: #414f5f;
          text-align: right;
          font-weight: 500;
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .request-history-container {
      .request-decision {
        padding: 20px;
        border-radius: 20px;
        font-size: 0.85rem;

        .request-decision__title {
          font-weight: 500;
          margin-bottom: 0;
        }

        .request-decision__body {
          color: rgba(65, 79, 95, 0.7);
          margin-bottom: 5px;
        }

        .request-decision__date {
          margin-bottom: 0px;
        }
      }
    }
  }
}
