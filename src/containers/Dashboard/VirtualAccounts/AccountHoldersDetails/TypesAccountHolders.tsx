import { IModalProps } from '+containers/Dashboard/Shared/Modal';
import { Dispatch, SetStateAction } from 'react';

export type TAccountHolderModal = {
  modalType: string | null;
  modalInfo: IModalProps | null;
};

export type TManageAccountModal = {
  modalType: string | null;
  modalInfo: IModalProps;
};

export type TKycModalType = 'approve' | 'decline' | 'success' | null;

export type TAccountHolderSummary = {
  [key in 'account_status' | 'account_type' | 'phone' | 'email' | 'occupation' | 'date_of_birth' | 'use_case']: string;
} & { date_verified: string | null; verified_identity: Record<'type' | 'number', string> };

export type TAccountHolderDocument = {
  [key in 'selfie' | 'identification' | 'source_of_inflow' | 'identification_back']: {
    url: string;
  };
} & {
  proof_of_address: {
    type: string;
    document: {
      url: string;
    };
  };
};

export type TKycData = {
  account_summary: TAccountHolderSummary;
  documents: TAccountHolderDocument;
  documents_upload_completed: boolean;
};

export type TAccountHolderName = Record<'first_name' | 'last_name', string>;

export type TAccountHolderData = TAccountHolderName &
  Omit<TAccountHolderSummary, 'account_status' | 'date_verified' | 'phone'> & {
    [key in 'reference' | 'phone_number' | 'status' | 'status_reason' | 'date_created' | 'country']: string;
  } & {
    address: {
      [key in 'zip' | 'city' | 'state' | 'address' | 'country']: string;
    };
    metadata: Record<string, string>;
  } & {
    employment: {
      [key in 'status' | 'employer' | 'description']: string;
    };
  };

export type TAccountHolderTransaction = {
  [key in 'amount' | 'created_at' | 'currency' | 'status' | 'transaction_date' | 'reference']: string;
} & { payment: { reference: string } };

export type TVirtualBankAccount = {
  [key in
    | 'status'
    | 'account_number'
    | 'account_name'
    | 'korapay_reference'
    | 'account_reference'
    | 'bank_name'
    | 'iban'
    | 'tier'
    | 'currency'
    | 'created_at']: string;
} & {
  merchant: {
    name: string;
    id: number;
  };
  account_holder: TAccountHolderName;
};

export type TEvent = {
  [key in 'reference' | 'event_type' | 'description' | 'event_action' | 'initiator' | 'created_at']: string;
} & {
  account_holder: TAccountHolderName;
};

export type TData = TKycData & TAccountHolderData;

export type TAccountType = 'individual' | 'corporate';
export type TFormField = {
  reason: string;
};

type TSetModalType = { setModalType: Dispatch<SetStateAction<TKycModalType>> };

export type TDeclineKycModal = TSetModalType & {
  formSubmit: (value: string) => void;
};

export type TApproveKycModal = TSetModalType & {
  dispatch: () => void;
};

export type TKycActionFeedbackModal = TSetModalType & {
  action: string;
};

export type TKycInformation = {
  data: TData;
};

export type TKycHistoryData = {
  status: 'approved' | 'rejected';
  decline_reason: string | null;
  created_at: string;
  account_holder: {
    [key in 'first_name' | 'last_name' | 'reference']: string;
  };
};
