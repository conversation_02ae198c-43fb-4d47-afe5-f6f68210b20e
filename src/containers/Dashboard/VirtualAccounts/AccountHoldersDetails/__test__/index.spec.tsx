import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';

import { mockedVbaHolder } from '+mock/mockData';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';
import { server } from '+mock/mockServers';

import AccountHolderDetails from '../index';

const MockedAccountHolderDetails = () => {
  return (
    <MockIndexWithRoute
      route="/dashboard/virtual-accounts/account-holders/:id"
      initialEntries={['/dashboard/virtual-accounts/account-holders/test']}
    >
      <AccountHolderDetails />
    </MockIndexWithRoute>
  );
};

describe('AccountHolderDetails', () => {
  test('AccountHolderDetails is accessible', async () => {
    const { container } = render(<MockedAccountHolderDetails />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Fetch Events data and render the events table when a user selects the Event in the nav section', async () => {
    render(<MockedAccountHolderDetails />);

    expect(await screen.findByText('(0) Account Numbers')).toBeInTheDocument();
    const summaryInfo = await screen.findByText(/Here's a summary of this account holder/i);
    expect(summaryInfo).toBeInTheDocument();
    expect(screen.getByText(/first name/i)).toBeInTheDocument();
    expect(screen.getByText(/last name/i)).toBeInTheDocument();
    expect(screen.getByText(/email/i)).toBeInTheDocument();
    expect(screen.getByText(/account holder status/i)).toBeInTheDocument();
    expect(screen.getByText(/use case/i)).toBeInTheDocument();
    expect(screen.getAllByText(/date created/i)?.[0]).toBeInTheDocument();
    expect(screen.getByText(/date verified/i)).toBeInTheDocument();
  });

  test('Fetch Events data and render the events table when a user selects the Event in the nav section', async () => {
    render(<MockedAccountHolderDetails />);
    expect(await screen.findByText('(0) Account Numbers')).toBeInTheDocument();
    const eventBtn = await screen.findByText(/events/i);
    expect(eventBtn).toBeInTheDocument();

    await userEvent.click(eventBtn);

    await waitFor(() => {
      expect(screen.getByTestId('table_comp')).toBeInTheDocument();
    });
  });

  test('Events should render two row data equal with the length of the data of the event mockData', async () => {
    render(<MockedAccountHolderDetails />);

    expect(await screen.findByText('(0) Account Numbers')).toBeInTheDocument();
    const eventBtn = await screen.findByText(/events/i);

    await userEvent.click(eventBtn);
    await waitFor(() => {
      expect(screen.getByText('Sarah')).toBeInTheDocument();
    });

    expect(screen.getByText(/Admin approved account holder/i)).toBeInTheDocument();
    expect(screen.getByText(/Merchant requested to create account holder/i)).toBeInTheDocument();
  });

  test('Suspend modal should not render the tell us why input field unless the user chooses the "other" option', async () => {
    render(<MockedAccountHolderDetails />);

    expect(await screen.findByText('(0) Account Numbers')).toBeInTheDocument();
    const actionBtn = await screen.findByText(/manage account holder/i);
    await userEvent.click(actionBtn);
    await waitFor(() => screen.findByText(/suspend account holder/i));

    const suspendBtn = screen.getByText(/suspend account holder/i);
    await userEvent.click(suspendBtn);

    expect(screen.queryByTestId('reason-text')).not.toBeInTheDocument();
  });

  test('Deactivate modal should only render the tell us why field', async () => {
    render(<MockedAccountHolderDetails />);
    expect(await screen.findByText('(0) Account Numbers')).toBeInTheDocument();
    const actionBtn = await screen.findByText(/manage account holder/i);
    await userEvent.click(actionBtn);
    await screen.findByText(/deactivate account holder/i);

    const suspendBtn = screen.getByText(/deactivate account holder/i);
    await userEvent.click(suspendBtn);
    await waitFor(() => expect(screen.getByTestId('second-button')).toBeDisabled());

    expect(screen.queryByTestId('select-reason')).not.toBeInTheDocument();
    expect(screen.getByTestId('reason-text')).toBeInTheDocument();
  });

  test('Suspend modal should render the tell us why input field when user chooses the "other" option', async () => {
    render(<MockedAccountHolderDetails />);
    expect(await screen.findByText('(0) Account Numbers')).toBeInTheDocument();
    const actionBtn = await screen.findByText(/manage account holder/i);
    await userEvent.click(actionBtn);
    await screen.findByText(/suspend account holder/i);

    const suspendBtn = await screen.findByText(/suspend account holder/i);
    await userEvent.click(suspendBtn);
    expect(await screen.findByTestId('second-button')).toBeDisabled();

    const selectReasonDropdown = screen.getByTestId('select-reason');

    await userEvent.selectOptions(selectReasonDropdown, 'other');
    expect(screen.getByTestId('reason-text')).toBeInTheDocument();
  });

  test('test suspend account holder flow', async () => {
    render(<MockedAccountHolderDetails />);
    expect(await screen.findByText('(0) Account Numbers')).toBeInTheDocument();
    const actionBtn = await screen.findByText(/manage account holder/i);
    await userEvent.click(actionBtn);
    await screen.findByText(/suspend account holder/i);

    const suspendBtn = screen.getByText(/suspend account holder/i);
    await userEvent.click(suspendBtn);
    await waitFor(() => expect(screen.getByTestId('second-button')).toBeDisabled());

    const selectReasonDropdown = screen.getByTestId('select-reason');

    await userEvent.selectOptions(selectReasonDropdown, 'suspected_fraud');
    await waitFor(() => expect((selectReasonDropdown as HTMLSelectElement).value).toBe('suspected_fraud'));

    await waitFor(() => expect(screen.getByTestId('second-button')).toBeEnabled());

    await userEvent.click(screen.getByTestId('second-button'));
    await screen.findByText(/yes, suspend/i);

    await userEvent.click(screen.getByTestId('second-button'));
    await screen.findByText(/dismiss/i);
  });

  test('test deactivate account flow', async () => {
    render(<MockedAccountHolderDetails />);
    expect(screen.getByText('(0) Account Numbers')).toBeInTheDocument();
    const actionBtn = await screen.findByText(/manage account holder/i);
    await userEvent.click(actionBtn);
    await screen.findByText(/deactivate account holder/i);

    const deactivateBtn = screen.getByText(/deactivate account holder/i);
    await userEvent.click(deactivateBtn);
    await waitFor(() => expect(screen.getByTestId('second-button')).toBeDisabled());

    await userEvent.type(screen.getByTestId('reason-text'), 'User is fraudulent');
    await waitFor(() => expect(screen.getByTestId('second-button')).toBeEnabled());

    await userEvent.click(screen.getByTestId('second-button'));
    await screen.findByText(/yes, deactivate/i);

    await userEvent.click(screen.getByTestId('second-button'));
    await screen.findByText(/dismiss/i);
  });
  test('success modal does not render if request is not successful', async () => {
    server.use(
      http.post('/admin/virtual-bank-account/account-holders/manage', () => {
        return HttpResponse.json({ error: 'Forbidden' }, { status: 403 });
      })
    );
    render(<MockedAccountHolderDetails />);
    expect(screen.getByText('(0) Account Numbers')).toBeInTheDocument();
    const actionBtn = await screen.findByText(/manage account holder/i);
    await userEvent.click(actionBtn);
    await screen.findByText(/deactivate account holder/i);

    const deactivateBtn = screen.getByText(/deactivate account holder/i);
    await userEvent.click(deactivateBtn);
    await waitFor(() => expect(screen.getByTestId('second-button')).toBeDisabled());

    await userEvent.type(screen.getByTestId('reason-text'), 'User is fraudulent');
    await waitFor(() => expect(screen.getByTestId('second-button')).toBeEnabled());

    await userEvent.click(screen.getByTestId('second-button'));
    await screen.findByText(/yes, deactivate/i);

    await userEvent.click(screen.getByTestId('second-button'));
    await screen.findByText(/yes, deactivate/i);
  });

  test('approved account holder option should render the suspend account option', async () => {
    render(<MockedAccountHolderDetails />);
    expect(screen.getByText('(0) Account Numbers')).toBeInTheDocument();
    const actionBtn = await screen.findByText(/manage account holder/i);
    await userEvent.click(actionBtn);

    await screen.findByText(/suspend account holder/i);
  });

  test('suspend account holder option should not render if account has already been suspended', async () => {
    const data = { ...mockedVbaHolder };
    data.data.status = 'suspended';
    server.use(
      http.get('/admin/virtual-bank-account/account-holders/test', () => {
        return HttpResponse.json(data, { status: 200 });
      })
    );
    render(<MockedAccountHolderDetails />);
    expect(screen.getByText('(0) Account Numbers')).toBeInTheDocument();
    const actionBtn = await screen.findByText(/manage account holder/i);
    await userEvent.click(actionBtn);

    await waitFor(() => expect(screen.queryByText(/suspend account holder/i)).not.toBeInTheDocument());
  });

  test('a deactivated account should not have the manage account holder options when viewed', async () => {
    const data = { ...mockedVbaHolder };
    data.data.status = 'deactivated';
    server.use(
      http.get('/admin/virtual-bank-account/account-holders/test', () => {
        return HttpResponse.json(data, { status: 200 });
      })
    );

    render(<MockedAccountHolderDetails />);

    expect(screen.getByText('(0) Account Numbers')).toBeInTheDocument();
    expect(screen.queryByText(/manage account holder/i)).not.toBeInTheDocument();
  });
});
