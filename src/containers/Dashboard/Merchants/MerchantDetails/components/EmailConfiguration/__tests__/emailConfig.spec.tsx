import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { toHaveNoViolations } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import EmailConfiguration from '../index';

const { useParams, feedbackInit, logError } = vi.hoisted(() => {
  return { useParams: vi.fn(), feedbackInit: vi.fn(), logError: vi.fn() };
});
vi.mock('react-router-dom', async () => {
  const actualReactRouter = await vi.importActual('react-router-dom');
  return {
    ...actualReactRouter,
    useParams
  };
});
function MockedEmailConfiguration() {
  return (
    <MockIndex>
      <EmailConfiguration />
    </MockIndex>
  );
}

vi.mock('+utils', async () => {
  const actualUtils = await vi.importActual('+utils');
  return {
    ...actualUtils,
    logError
  };
});

vi.mock('+hooks/useFeedbackHandler', async () => {
  const actualFeedBackHandler = await vi.importActual('+hooks/useFeedbackHandler');
  return {
    ...actualFeedBackHandler,
    feedbackInit
  };
});
expect.extend(toHaveNoViolations);

describe('EmailConfiguration_function', () => {
  test('should display fetched email channels', async () => {
    useParams.mockImplementation(() => ({ id: '1' }));
    render(<MockedEmailConfiguration />);
    const configEmailInput = screen.getByTestId('config-email-input');
    await waitFor(() => {
      expect(configEmailInput).toBeInTheDocument();
      const expectedValue = /davido@korapay\.com,\s?peace@korapay\.com/;
      expect(configEmailInput.value).toMatch(expectedValue);
    });
  });

  test('Feedback handler is called when notifications endpoint fails', async () => {
    useParams.mockImplementation(() => ({ id: '2' }));
    feedbackInit({
      message: 'There has been an error getting merchant settlement summary',
      type: 'danger',
      action: undefined,
      statusCode: undefined,
      componentLevel: undefined,
      callback: undefined,
      title: undefined
    });
    logError();
    render(<MockedEmailConfiguration />);
    await waitFor(() => {
      expect(feedbackInit).toHaveBeenCalledTimes(1);
      expect(feedbackInit).toHaveBeenCalledWith({
        message: 'There has been an error getting merchant settlement summary',
        type: 'danger',
        action: undefined,
        statusCode: undefined,
        componentLevel: undefined,
        callback: undefined,
        title: undefined
      });
    });
  });

  test('Feedback handler is called when merchant details endpoint fails', async () => {
    useParams.mockImplementation(() => ({ id: '2' }));
    feedbackInit({
      message: `There has been an error getting this merchant's details`,
      type: 'danger',
      action: undefined,
      statusCode: undefined,
      componentLevel: undefined,
      callback: undefined,
      title: undefined
    });
    logError();
    render(<MockedEmailConfiguration />);
    await waitFor(() => {
      expect(feedbackInit).toHaveBeenCalledTimes(1);
      expect(feedbackInit).toHaveBeenCalledWith({
        message: `There has been an error getting this merchant's details`,
        type: 'danger',
        action: undefined,
        statusCode: undefined,
        componentLevel: undefined,
        callback: undefined,
        title: undefined
      });
    });
  });
});
