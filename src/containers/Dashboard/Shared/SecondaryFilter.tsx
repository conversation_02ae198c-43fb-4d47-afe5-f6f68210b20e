import React from 'react';

import { IDateFilterProps } from '+types';

import DateFilter from './DateFilter';
import SecondarySearch from './SecondarySearch';

import './SecondaryFilter.scss';

const SecondaryFilter = ({
  selectedDate,
  onDateChange,
  type,
  handleSearch,
  state
}: IDateFilterProps & {
  type: 'pay-in' | 'pay-outs' | 'paused-payments';
  handleSearch: (value: Record<string, unknown>) => void;
  state: Record<string, unknown>;
}) => {
  const fullWidth = ['merchant-team-members'].includes(type);
  return (
    <div className={`secondary-filter--container ${fullWidth ? '--full-width' : ''}`}>
      {!['merchant-team-members'].includes(type) && (
        <DateFilter tableType={type} selectedDate={selectedDate} onDateChange={onDateChange} isCleared={state.clear as boolean} />
      )}
      <SecondarySearch type={type} handleSearch={handleSearch} state={state} />
    </div>
  );
};

export default SecondaryFilter;
