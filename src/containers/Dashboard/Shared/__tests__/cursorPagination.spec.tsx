import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, Mock, vi } from 'vitest';

import { useSearchQuery } from '+hooks';

import CursorPagination from '../AdvancedCursorPagination';

// Mock the useSearchQuery hook
vi.mock('+hooks', () => ({
  useSearchQuery: vi.fn()
}));

describe('CursorPagination', () => {
  const mockSetQuery = vi.fn();
  const mockSearchQuery = {
    value: {
      totalItems: '100',
      page: '1'
    },
    setQuery: mockSetQuery
  };

  beforeEach(() => {
    (useSearchQuery as Mock).mockReturnValue(mockSearchQuery);
  });

  it('renders pagination description', () => {
    render(<CursorPagination cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    expect(screen.getAllByText(/10/)).toHaveLength(3);
    expect(screen.getByText(/100/)).toBeInTheDocument();
    expect(screen.getByText(/back to top/i)).toBeInTheDocument();
    expect(screen.queryByText(/You can only choose the number of items to display from the first page./i)).not.toBeInTheDocument();
  });

  it('tooltip should display on page 2', () => {
    const mockPageSearchQuery = {
      value: {
        totalItems: '100',
        page: '2'
      },
      setQuery: mockSetQuery
    };
    (useSearchQuery as Mock).mockReturnValue(mockPageSearchQuery);
    render(<CursorPagination cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    expect(screen.getAllByText(/10/)).toHaveLength(2);
    expect(screen.getByText(/100/)).toBeInTheDocument();
    expect(screen.getByText(/back to top/i)).toBeInTheDocument();
    expect(screen.getByText(/You can only choose the number of items to display from the first page./i)).toBeInTheDocument();
  });

  it('disables previous button on first page', () => {
    render(<CursorPagination cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const prevButton = screen.getByLabelText('Previous Page');
    expect(prevButton).toBeDisabled();
  });

  it('disable next button when there is no next cursor', () => {
    render(<CursorPagination cursors={{ previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const nextButton = screen.getByLabelText('Next Page');
    expect(nextButton).toBeDisabled();
  });

  it('enables next button when there is a next cursor', () => {
    render(<CursorPagination cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const nextButton = screen.getByLabelText('Next Page');
    expect(nextButton).not.toBeDisabled();
  });

  it('disables the limit field non first page', () => {
    const disableMockSearchQuery = {
      value: {
        totalItems: '100',
        page: '2'
      },
      setQuery: mockSetQuery
    };
    (useSearchQuery as Mock).mockReturnValue(disableMockSearchQuery);
    render(<CursorPagination cursors={{ next_cursor: 10, previous_cursor: 9, total_items: 100, page_size: 10 }} totalCount={10} />);

    const limitField = screen.getByTestId('pagination_limit');
    expect(limitField).toBeDisabled();
  });

  it('enables the limit field on the first page', () => {
    render(<CursorPagination cursors={{ next_cursor: 2, total_items: 100, page_size: 10 }} totalCount={10} />);

    const limitField = screen.getByTestId('pagination_limit');
    expect(limitField).not.toBeDisabled();
  });

  it('calls setQuery with correct params on next button click', async () => {
    render(<CursorPagination cursors={{ next_cursor: 2, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const nextButton = screen.getByLabelText('Next Page');
    await userEvent.click(nextButton);

    expect(mockSetQuery).toHaveBeenCalledWith({
      pageCursorValue: '2',
      pageAction: 'next',
      page: '2'
    });
  });

  it('calls setQuery with correct params on previous button click', async () => {
    mockSearchQuery.value.page = '2';
    render(<CursorPagination cursors={{ next_cursor: 3, previous_cursor: 1, total_items: 100, page_size: 10 }} totalCount={10} />);

    const prevButton = screen.getByLabelText('Previous Page');
    await userEvent.click(prevButton);

    expect(mockSetQuery).toHaveBeenCalledWith({
      pageCursorValue: '1',
      pageAction: 'previous',
      page: '1'
    });
  });
});
