import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import DateFilter from '+shared/DateFilter';

const handleSearch = vi.fn();

const MockSecondarySearch = () => {
  return (
    <MockIndex>
      <DateFilter onDateChange={handleSearch} isCleared={false} selectedDate={{ startDate: '', endDate: '' }} />
    </MockIndex>
  );
};

describe('DateFilter', () => {
  it('DateFilter is accessible', async () => {
    const { container } = render(<MockSecondarySearch />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should render', () => {
    render(<MockSecondarySearch />);
    const dateFilterContainer = screen.getAllByRole('presentation');
    const dateInput = screen.getByTestId('dateFilter');
    expect(dateFilterContainer).toHaveLength(1);
    expect(dateInput).toBeInTheDocument();
    expect(dateInput).toHaveValue('Today');
  });

  it('Should render date options when the date is clicked', async () => {
    render(<MockSecondarySearch />);
    const dateFilterContainer = screen.getAllByRole('presentation');
    const dateInput = screen.getByTestId('dateFilter');
    await userEvent.click(dateFilterContainer[0]);

    expect(screen.getByText('Today')).toBeInTheDocument();
    expect(screen.getByText('Last 7 days')).toBeInTheDocument();
    expect(screen.getByText('Last 30 days')).toBeInTheDocument();
    expect(screen.getByText('Last 90 days')).toBeInTheDocument();
    expect(screen.getByText('All Time')).toBeInTheDocument();
    expect(screen.getByText('Custom Range')).toBeInTheDocument();

    const options = screen.getAllByRole('presentation');
    expect(options).toHaveLength(7);
    await userEvent.click(options[2]);
    expect(dateInput).toHaveValue('Last 7 days');
  });

  it('Should render calendar when custom range is selected', async () => {
    Date.now = vi.fn().mockReturnValue(new Date('2024-02-23'));
    render(<MockSecondarySearch />);
    const dateFilterContainer = screen.getAllByRole('presentation');
    await userEvent.click(dateFilterContainer[0]);

    const options = screen.getAllByRole('presentation');

    await userEvent.click(options[6]);
    await screen.findByText('Custom Date Range');
    await screen.findByText('Select the start and end date to limit your search query.');
    await screen.findByText('From (Start Date)');
    await screen.findByText('To (End Date)');
    await screen.findByLabelText('startDate');
    await screen.findByLabelText('endDate');
    await screen.findByTestId('first-button');
    await screen.findByTestId('second-button');
  });
});
