import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';

import AuditLogs from '../index';

const MockedAuditLogs = () => {
  return (
    <MockIndex>
      <AuditLogs />
    </MockIndex>
  );
};

describe('Audit Logs', () => {
  test('Audit Logs is accessible', async () => {
    const { container } = render(<MockedAuditLogs />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Renders page with correct tabs and heading', async () => {
    render(<MockedAuditLogs />);
    expect(screen.getByText('Events')).toBeInTheDocument();
    expect(screen.getByText('Last 24 Hours')).toBeInTheDocument();
    expect(screen.getByText('Last 7 Days')).toBeInTheDocument();
    expect(screen.getByText('Last 30 Days')).toBeInTheDocument();
    expect(screen.getByText('All Time')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('Event')).toBeInTheDocument();
      expect(screen.getByText('Event ID')).toBeInTheDocument();
    });
  });

  test('should fetch audit logs data', async () => {
    render(<MockedAuditLogs />);

    const description = ': Godis Agu (<EMAIL>) logged into the admin dashboard';
    const eventId = 'evt_Rtrdu_331709820847972';

    await waitFor(() => {
      expect(screen.getByText(eventId)).toBeInTheDocument();
      expect(screen.getByText(description)).toBeInTheDocument();
    });
  });
});
