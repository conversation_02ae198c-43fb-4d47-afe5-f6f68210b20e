.irc {
  background-color: #f9fbfd;
  align-items: center;
  padding: 20px;

  &.disabled {
    opacity: 0.7;
  }

  &__confirm-modal-content {
    background-color: #f3f4f8;
    padding: 20px;
    > h5 {
      font-size: 0.875rem;
      font-weight: 500;
      margin-bottom: 10px;
      line-height: 22.8px;
      color: #a9afbc;
    }
    > p {
      font-size: 0.875rem;

      color: #414f5f;
    }

    &.warning {
      background-color: #fff7ed;
      border-radius: 5px;

      p {
        color: #915200;
        font-weight: 500; 
        margin-bottom: 0px;
      }
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;

    & > div {
      @media screen and (max-width: 768px) {
        width: 60%;
      }

      @media screen and (max-width: 576px) {
        width: 100%;
      }
    }

    &-btn {
      background-color: #2376f3;
      color: #fff;
      padding: 10px 20px;
      border-radius: 5px;

      &:hover {
        background-color: #1e6be0;
      }

      &:active {
        background-color: #1a5fd2;
      }
    }
  }

  &__title {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 10px;
    line-height: 22.8px;
  }

  &__description {
    font-size: 0.875rem;
    color: #a9afbc;
    max-width: 500px;
  }

  &__content {
    margin-top: 20px;
    border-top: 2px solid #f1f6fa;
    padding-top: 20px;

    &-title {
      font-size: 0.93rem;
      font-weight: 500;
      margin-bottom: 10px;
      line-height: 22.8px;
    }
  }

  &__region {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;
    position: relative;
    padding: 12px 0;
    justify-content: space-between;
    &-name {
      font-size: 0.875rem;
      font-weight: 400;
      color: #414f5f;
      gap: 10px;
      display: flex;
      align-items: center;

      > .region-flag {
        width: 24px;
        border-radius: 2px;
        overflow: hidden;

        svg: {
          width: 100%;
          height: 100%;
        }
      }
    }
    &.unavailable {
      opacity: 0.7;
      text-align: center;
      width: 100%;
      font-style: italic;
    }
    &-right {
      display: flex;
      align-items: center;
      gap: 2rem;

      @media screen and (max-width: 1024px) {
        width: 60%;
      }

      @media screen and (max-width: 768px) {
        width: 50%;
      }
    }

    &-warning {
      span {
        margin-left: 8px;
        color: #94a7b7;
        font-style: italic;
        font-weight: 500;
      }
      @media screen and (max-width: 768px) {
        display: none;
      }
    }

    &-status {
      font-size: 0.875rem;
      font-weight: 500;
      border-radius: 5px;
      padding: 5px 10px;

      &.enabled {
        background-color: rgba(228, 255, 241, 1);
        color: rgba(36, 179, 20, 1);
      }

      &.disabled {
        background-color: rgba(241, 246, 250, 1);
        color: rgba(65, 79, 95, 1);
      }
    }
    &-btns {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      align-items: flex-end;
      justify-content: flex-end;
      .btn {
        padding: 5px 10px;
        border-radius: 5px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 5px;
      }
      .btn-enable {
        background-color: rgba(36, 179, 20, 1);
        color: rgba(255, 255, 255, 1);

        &.disabled {
          background-color: rgba(221, 226, 236, 1);
          color: rgba(255, 255, 255, 1);
        }
      }

      .btn-disable {
        background-color: rgba(234, 242, 254, 1);
        color: #414f5f;

        &.disabled {
          background-color: rgba(221, 226, 236, 1);
          color: rgba(65, 79, 95, 1);
        }
      }
    }
  }

  &__region + &__region {
    border-top: 1px solid #dde2ec;
  }
}
