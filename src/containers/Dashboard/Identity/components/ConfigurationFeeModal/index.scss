.icf {
  background-color: #f1f6fa;
  border-radius: 10px;
  padding: 10px 20px;
  color: #414f5f;

  &__item-checkbox {
    width: 100%;

    > button {
      width: 80%;
    }
  }

  &.no-content {
    text-align: center;
    font-style: italic;
    font-weight: 500;
    color: #94A7B7;
  }

  &.not-available {
    font-style: italic;
    text-align: center;
    font-weight: 600;
  }

  & + & {
    margin-top: 20px;
  }

  &__header {
    border-bottom: 1px solid #DDE2EC;
    padding: 3px 0;
    > div {
      opacity: 0.6;
      font-weight: 500;
    }
  }

  &__item {
    &-title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      max-width: fit-content;
      opacity: 0.6;

    }
    > div:nth-child(1) {
        font-weight: 400;
    }

    > div:nth-child(2) {
      font-weight: 500;
    }
  }

  &__vat-info {
    font-size: 0.75rem;
    color: #414f5f;
    opacity: 0.7;
    margin-left: 25px;
  }

  &__header,
  &__item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;

    & > div:nth-child(1) {
      width: 50%;
      display: flex;
      align-items: center;
      gap: 5px;
  

      .id-tooltip {
        margin-top: -5px;
        margin-right: 5px;
      }
    }
    & > div:nth-child(2) {
      width: 20%;
    }
    & > div:nth-child(3) {
      width: 30%;
      display: flex;
      justify-content: flex-end;

      &.reset {
        font-weight: 500;
      }
      > input:nth-child(1) {
        width: 40%;
        border: 2px solid #DDE2EC;
        border-radius: 5px 0px 0px 5px;
        padding-left: 5px;
      }
      > input:nth-child(2) {
        width: 60%;
        border: 2px solid #DDE2EC;
        border-left: none;
        padding: 5px;
        text-align: right;
        border-radius: 0px 5px 5px 0px;
      }

      > select {
        appearance: auto;
        width: 50%;
        border: 2px solid #DDE2EC;
        padding: 5px;
        border-radius: 5px 0px 0px 5px;
      }
    }
  }
  &__content {
    padding-top: 15px;
  
  }

  &__accordion {

    & + & {
      padding-top: 20px;
      border-top: #DDE2EC 1px solid;

      &.no-content {
        border-top: none;
      }
    }
    > summary {
      justify-content: space-between;
      margin-bottom: 20px;

      > section {
        font-weight: 600;
        font-size: 0.93rem;
      }
    }
  }
}

.fees-currency-input {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  > label {
    font-weight: 500;
  }

  > select {
    appearance: auto;
    max-width: 320px;
    border: 2px solid #DDE2EC;
    border-radius: 5px;
    padding: 5px 10px;
  }
}

.id-fees-reset {
  h3 {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 20px;
    color: #414F5F;
    margin-top: 20px;
  }

  > .reset-all {
    & > button {
      font-weight: 400 !important;
    }
    margin-bottom: 20px;

  }
}
