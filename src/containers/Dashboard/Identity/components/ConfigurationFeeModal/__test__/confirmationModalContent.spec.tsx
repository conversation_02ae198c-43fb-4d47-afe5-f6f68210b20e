import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';
import ConfirmationContent from '../ModalContent/Confirmation';

const MockedConfigurationModalContent = ({ type }: { type: 'reset' | 'configuration' }) => {
  return (
    <MockIndex>
      <ConfirmationContent type={type} />
    </MockIndex>
  );
};
describe('ConfirmationContent', () => {
  it('renders configuration confirmation message', () => {
    render(<MockedConfigurationModalContent type="configuration" />);
    expect(
      screen.getByText('Note: Ensure that merchants are capable of being charged in the currencies configured for these fees.')
    ).toBeInTheDocument();
  });

  it('renders reset confirmation message', () => {
    render(<MockedConfigurationModalContent type="reset" />);
    expect(
      screen.getByText(
        'Please confirm that you want to reset these fees back to default. This will override this merchant’s current fees with default system fees for the selected ID types.'
      )
    ).toBeInTheDocument();
  });

  it('applies warning class for reset type', () => {
    render(<MockedConfigurationModalContent type="reset" />);
    const divElement = screen.getByText(
      'Please confirm that you want to reset these fees back to default. This will override this merchant’s current fees with default system fees for the selected ID types.'
    ).parentElement;
    expect(divElement).toHaveClass('warning');
  });

  it('does not apply warning class for configuration type', () => {
    render(<MockedConfigurationModalContent type="configuration" />);
    const divElement = screen.getByText(
      'Note: Ensure that merchants are capable of being charged in the currencies configured for these fees.'
    ).parentElement;
    expect(divElement).not.toHaveClass('warning');
  });
  it('has no accessibility violations', async () => {
      const { container } = render(<MockedConfigurationModalContent  type="configuration"/>);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
});
