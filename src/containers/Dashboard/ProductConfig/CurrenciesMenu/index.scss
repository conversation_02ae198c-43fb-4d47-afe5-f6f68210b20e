@import 'styles/kpy-custom/_custom';
@import 'styles/kpy-custom/variables';

.menu-container {
  font-family: 'Averta PE';
  padding: 2rem 3rem;

  &__heading > * + * {
    margin-top: 10px !important;
  }

  .menu {
    padding-top: 5rem;

    & > * + * {
      margin-top: 10px;
    }

    .table {
      border: 2px solid #f1f6fa;
      padding: 1rem 1.5rem 0 1.5rem;
      border-radius: 5px;

      .product-config-row {
        display: flex;
        justify-content: space-between;
        column-gap: 1rem;
        font-family: 'Averta PE';
        font-size: 14px;
        margin-block: 1.2rem;

        &__currency {
          font-weight: 500;
          color: #292b2c;

          line-height: 20px;

          @media (min-width: 1200px) {
            width: 18rem;
          }

          p {
            margin-bottom: 0 !important;
          }
        }

        &__merchants {
          text-align: left;
          font-weight: 300;
          color: #414f5f;
          opacity: 60%;
          display: flex;
          justify-content: flex-end;
          align-items: center;

          > p {
            margin: 0;
          }
        }

        &__links {
          display: flex;
          align-items: center;
          flex-grow: 0.8;

          > * {
            flex-basis: 0;
            flex-grow: 1;
          }
        }

        &__product,
        &__merchant {
          color: #2376f3;
          font-weight: 600;
          display: flex;
          align-items: center;
          justify-content: center;

          a {
            text-decoration: none;
            color: #2376f3;
          }

          a:hover {
            opacity: 0.5;
            transition: all 0.1s ease-in-out;
          }
        }
      }
    }
  }
}
