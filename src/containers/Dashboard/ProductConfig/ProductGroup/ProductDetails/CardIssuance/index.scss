@import 'styles/kpy-custom/variables';

.btn-primary[data-variant='cta-add-merchant'] {
  padding-block: 0.95rem;
  padding-inline: 1.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: 0.5rem;

  &>.os-icon-plus {
    font-size: 1.125rem;
  }
}

// EDIT VIRTUAL CARDS LIMITS FORM
.edit-vc-limits-form>*+* {
  margin-top: 2rem;
}

.edit-vc-limits-table {
  --num-of-rows: 4;
  display: grid;
  grid-template-columns: auto 1fr;
  column-gap: 1.875rem;
  row-gap: 1.5rem;
  border-collapse: collapse;
  border-spacing: 0;
  vertical-align: middle;
  white-space: nowrap;
  overflow-x: auto;
  align-items: center;

  & .limit-title {
    display: flex;
    align-items: center;

    &>label {
      font-weight: 400;
      margin-block: 0;
    }
  }

  & th {
    color: #414f5f;
    font-weight: 500;

    &:not([scope='row']) {
      text-align: start;
    }
  }

  & td {
    color: #94a7b7;
    font-weight: 500;
    border-left: 0;
  }

  // THEAD
  & thead {
    display: grid;
    grid-row: span var(--num-of-rows);
    grid-template-rows: subgrid;
    min-width: min-content;

    &>tr {
      display: grid;
      grid-template-rows: subgrid;
      grid-row: span var(--num-of-rows);
      height: 100%;
    }
  }

  // TBODY
  & tbody {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: subgrid;
    grid-row: span var(--num-of-rows);
    column-gap: 1.875rem;
    overflow-x: auto;

    &>tr {
      display: grid;
      grid-template-rows: subgrid;
      grid-row: span var(--num-of-rows);
    }

    & input {
      font-weight: 400;
    }
  }

  & tbody.five-cols {
    grid-template-columns: repeat(5, 1fr);
  }

  & tbody.one-col {
    grid-template-columns: repeat(1, 0.5fr);
  }
}

// STEPPER
.stepper {
  gap: 0.25rem;

  & .step {
    padding: 0;
    border: 0;
    background-color: transparent;
    gap: 0.5rem;
  }

  & .label-with-step-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.5;

    &.active {
      opacity: 1;
    }
  }

  & .step-indicator {
    display: inline-block;
    height: 8px;
    width: 8px;
    background-color: #2376f3;
  }

  .step+.step {
    flex-grow: 1;

    & .dashed-line {
      border: 0.5px dashed #94a7b7;
      flex-grow: 1;
      height: 1px;
    }
  }
}

// LIMIT TOGGLE
.limit-toggle-with-stepper>*+* {
  border-top: 1px solid #dde2ec;
  padding-block-start: 1rem;
}

.radio-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &>label {
    order: 1;
    font-weight: 400;
  }
}

.limit-toggle-comp {
  margin-top: 0;
  padding-block-end: 1rem;

  & .controls>*+* {
    margin-top: 0.5rem;
  }
}

.limit-toggle-with-desc {
  &>*+* {
    margin-block-start: 1rem;
  }
}

// AMBER DISCLAIMER
.disclaimer {
  font-weight: 500;
  color: #915200;
  background-color: #fff7ed;
  border-radius: 8px;
  padding: 1rem;
}

// CONFIRMATION DISCLAIMER
.confirmation-disclaimer {
  color: #2376f3;
  display: flex;
  align-items: center;

  & svg {
    display: inline-block;
    align-self: center;
  }

  &>.copy {
    margin: 0;
    font-style: italic;
    font-weight: 500;
  }
}

.confirmation-check {
  margin-block-end: 1rem;
  display: flex;
  gap: 0.75rem;
  align-items: center;

  &.disabled>* {
    color: #94a7b7;
  }

  &>label {
    order: 1;
    margin: 0;
  }
}

// LIMIT SETTING HEADER
.limit-setting-header {
  display: flex;
  align-items: center;
  font-size: 1rem;

  >.context-title {
    font-weight: 700;
    padding-right: 0.5rem;
  }

  >.type-title {
    padding-left: 0.5rem;
    border-left: 1px solid #dde2ec;
    color: #94a7b7;
  }
}

// Utility
input:disabled~label[data-disabled='true'] {
  opacity: 0.5;
}

.default-configuration {
  margin-top: 2.5rem;

  &>p:first-child {
    font-size: 1rem;
    font-weight: 600;
    color: '#292B2C';
  }

  h5 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #3e4b5b;
    line-height: 28px;
    margin-top: 1.875rem;
  }

  .limits-input {
    display: flex;
    align-items: center;

    input {
      font-size: 1rem;
      font-weight: 400;
      color: #292b2c;
      line-height: 20px;
      border: 2px solid $kpyblue;
      border-radius: 0.375rem;
      padding: 0.5rem 1rem;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;

      &:focus {
        border-color: $kpyblue;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }

      &::placeholder {
        color: #dde2ec;
        font-weight: 500;
        font-size: 1rem;
      }
    }

    .limits-btn {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
      padding: 0.5rem 1rem;
      line-height: 20px;
    }
  }
}

.access-status {
  font-size: 16px;
  font-weight: 500;
  padding: 2px 8px;
  margin-left: 0.5rem;
  border-radius: 0.3rem;
}

.enable-access-btn {
  background: #eaf2fe;
  color: #414f5f;
  border: none;
  padding: 0.7rem 1.3rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 0.5rem;
}

.categories-section {
  margin-top: 2rem;

  p {
    font-size: 1rem;
    font-weight: 500;
    color: #a9afbc;
    margin-bottom: 20px;
  }
}

.card-issuance-product {
  padding-top: 1.5rem;
  margin-bottom: 15rem;

  .card-issuance-product__header {
    display: flex;
    justify-content: flex-start;
    gap: 1.5rem;
    align-items: baseline;
    margin-bottom: 1rem;
  }

  .subtitle {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    p {
      font-size: 1rem;
      font-weight: 500;
      color: #a9afbc;
    }

    button {
      background: #24b314;
      border: none;
      color: white;
      font-size: 0.9rem;
      font-weight: 600;
      padding: 0.7rem 1rem;
      border-radius: 0.5rem;
    }

    button:disabled {
      opacity: 0.5;
    }
  }

  .card-issuance-product__details {
    display: flex;
    flex-direction: row;
    gap: 30px;
    margin: 40px 0 60px;

    .card-issuance-product__details__item {
      display: flex;
      flex-direction: column;
      gap: 16px;

      p {
        font-size: 1rem;
        font-weight: 600;
        color: #3e4b5b;
        margin: 0;
      }

      span {
        font-size: 1rem;
        font-weight: 400;
        color: #a9afbc;
      }
    }
  }

  .card-issuance-product__footer {
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #414f5f;
    }

    span {
      font-size: 1rem;
      font-weight: 400;
      color: #94a7b7;
    }

    .card-issuance-product__footer__document {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 1rem;
      border: 3px solid #f3f4f8;
      border-radius: 0.6rem;
      background: #f9fbfd;
      padding: 0.5rem 1.25rem;

      div {
        display: flex;
        gap: 1.5rem;

        p {
          font-size: 1rem;
          font-weight: 500;
          color: #3e4b5b;
          margin-bottom: 0;
        }

        span {
          font-size: 1rem;
          font-weight: 400;
          color: #a9afbc;
        }
      }

      button {
        background: none;
        border: none;
        display: flex;
        align-items: center;

        span {
          margin-right: 0.5rem;
          color: $kpyblue;
          font-size: 0.9rem;
          font-weight: 600;
        }
      }
    }
  }
}

.modal-content {
  svg {
    margin-right: 0.5rem;
  }

  label {
    color: #414f5f;
    font-weight: 500;
  }

  .info {
    color: $kpyblue;
    font-size: 1rem;
    margin-block-end: 0.5rem;
    font-style: italic;
    font-weight: 500;
    display: flex;

    &>svg {
      flex-shrink: 0.3;
      font-size: 0.5rem;
    }
  }

  .checkbox-container {
    display: flex;
    align-items: center;

    input {
      margin: 0 10px -8px 0;
    }
  }
}

// LIMITS INFO COMPONENT
.limits-info-comp {
  padding: 1.5rem;
  border-radius: 8px;
  background: #f9fbfd !important;

  &>*+* {
    margin-top: 1rem;
  }

  &>.top-section {
    font-size: 1rem;

    &>.edit-limit-cta {
      font-weight: 600;
    }
  }

  & .title-block {
    &>.title {
      font-weight: 600;
      color: #292b2c;
      margin: 0;
    }

    &>.subtitle {
      color: #94a7b7;
      font-size: 0.9rem;
      margin-bottom: 0;
    }

    &>*+* {
      margin-top: 0.25rem;
    }
  }

  &>.limits-info-panel {
    display: grid;
    grid-template-columns: 0.5fr 1fr;
    border-collapse: collapse;
    border-spacing: 0;
    vertical-align: middle;
    white-space: nowrap;
    overflow-x: auto;
    align-items: center;

    & * {
      vertical-align: middle;
    }

    & tbody {
      -webkit-overflow-scrolling: touch;
    }

    & th,
    & td {
      padding-block: 0.5rem;
    }

    tr>*+* {
      border-top: 1px solid #dde2ec;
    }

    // THEAD
    & thead {
      display: grid;
      grid-row: span var(--num-of-rows);
      grid-template-rows: subgrid;
      min-width: min-content;

      &>tr {
        display: grid;
        grid-template-rows: subgrid;
        grid-row: span var(--num-of-rows);
        height: 100%;
      }
    }

    // TBODY
    & tbody {
      display: grid;
      grid-template-columns: repeat(var(--num-of-columns), 1fr);
      grid-template-rows: subgrid;
      grid-row: span var(--num-of-rows);
      overflow-x: auto;

      &>tr {
        display: grid;
        grid-template-rows: subgrid;
        grid-row: span var(--num-of-rows);

        &>* {
          padding-left: 1rem;
          text-align: var(--text-align);
        }
      }
    }
  }
}

// SUMMARY COMPONENT
.summaries {
  list-style: none;
  padding-inline: 0;
  font-size: 1rem;

  &[data-layout='horizontal'] {
    padding-block: 1rem;
    display: flex;
    align-items: start;
    gap: 1rem;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      display: block;
    }

    &>.summary+.summary {
      @media (max-width: 768px) {
        margin-top: 0.5rem;
        border-left: none;
        padding-left: 0;
      }

      border-left: 0.5px solid #a9afbc;
      padding-left: 0.75rem;
      margin-top: 0;
    }

    &>.summary:last-of-type {
      flex-grow: 1;
    }

    &>.summary {
      @media (max-width: 768px) {
        display: flex;
        flex-wrap: nowrap;
        gap: 1rem;
      }

      flex-grow: 2;
    }

    & .summary-label {
      font-weight: 400;

      @media (max-width: 768px) {
        max-width: 30ch;
        min-width: min(30%, 30ch);
      }
    }

    & .summary-value {
      @media (max-width: 768px) {
        display: flex;
        justify-content: end;
        align-items: center;
        text-align: right;
        min-width: 0;
        overflow-wrap: break-word;
        flex-grow: 1;
        margin-top: 0;
      }

      margin-top: 0.5rem;
      font-weight: 600;
    }
  }

  &[data-layout='vertical'] {
    &>.section-list {
      border-top: 0.5px solid #a9afbc;
      padding-block: 1rem;
    }

    &>.summary+.summary {
      margin-top: 1rem;
    }

    .summary-label {
      color: #a9afbc;
      font-weight: 500;
      flex-grow: 1;
      max-width: 30ch;
      min-width: min(30%, 30ch);
    }

    .summary-value {
      flex-grow: 1;
      max-width: 70ch;

      @media (max-width: 768px) {
        text-align: right;
        min-width: 0;
        overflow-wrap: break-word;
      }
    }
  }
}

.requesters {
  &__service-status {
    width: 9px;
    height: 9px;
    border-radius: 50%;
    margin-right: 4px;

    &.--active {
      background-color: rgba(36, 179, 20, 1);
    }

    &.--inactive {
      background-color: rgba(169, 175, 188, 1);
    }

    &.--pending {
      background-color: rgba(250, 149, 0, 1);
    }
  }

  &__accordion {
    >summary {
      width: 100%;
      justify-content: space-between;
      font-weight: 500;

      section:first-of-type {
        font-size: 20px;
      }
    }

    .mismatched-error-msg {
      color: #fa9500;
      font-weight: 500;
      margin-top: 20px;

      >span {
        margin-left: 5px;
      }
    }

    &+& {
      margin-top: 20px;
    }

    &__limits {
      >summary {
        padding-top: 10px;

        section:first-of-type {
          font-weight: 500;
          font-size: 16px;
        }
      }
    }
  }
}

.cards-config-panel {

  &>*+*,
  &__main>*+* {
    margin-top: 50px;
  }

  &__header>*+* {
    margin-top: 20px;
  }

  &__toggle-creation>*+* {
    margin-top: 16px;
  }

  &__header>.header__section {
    gap: 2rem;
  }

  & .toggle-creation__check {
    margin-block-end: 1rem;
    display: flex;
    gap: 0.75rem;
    align-items: center;

    &.disabled>* {
      color: #94a7b7;
    }

    &>label {
      order: 1;
      margin: 0;
    }
  }

  & .toggle-creation__button-container {
    gap: 1em;

    &>.divider-x {
      display: inline-block;
      width: 1px;
      height: 1.5rem;
      background-color: #d5d8db;
    }
  }

  &__access-message {
    gap: 10px;
    padding: 20px;
    background-color: #f1f6fa;
    border-radius: 10px;

    >svg {
      flex-shrink: 0;
    }

    >span {
      font-weight: 600;
      font-size: 1rem;
    }
  }

  &__change-partner>*+* {
    margin-top: 16px;
  }

  & .change-partner {
    &__container {
      display: block;
      padding: 1.875rem 2.5rem;
      border-radius: 8px;
      background: #f9fbfd !important;
      font-size: 1rem;
    }

    &__section {
      line-height: 20px;
      font-weight: 400;

      &>* {
        font-size: 0.9rem;
        line-height: 20px;
        font-weight: 400;
      }
    }

    &__description {
      color: #94a7b7;
      max-width: 60ch;
    }

    &__current-partner {
      color: #414f5f;

      &> :first-child {
        font-weight: 600;
      }
    }
  }

  &__limits-section>*+* {
    margin-top: 16px;
  }

  & .limits-section__heading {
    font-size: 1rem;
  }

  & .limits-section__info-panels-container>*+* {
    margin-top: 16px;
  }
}
