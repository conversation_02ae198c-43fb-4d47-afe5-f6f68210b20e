import { screen } from '@testing-library/react';

import { mockIssuingMerchantDetails } from '+mock/mockData';
import useProductConfigCardIssuanceStore from '+store/productConfigCardIssuanceStore';
import { formatAmount, getDateAndTime } from '+utils';

import { renderMockHolderDetails } from './common';

const mockData = mockIssuingMerchantDetails.data;

useProductConfigCardIssuanceStore.setState({
  access: {
    issuedIsEnabled: false,
    reservedIsEnabled: true
  }
});

describe('HolderDetails component', () => {
  test('Renders details for a merchant with access', async () => {
    renderMockHolderDetails();

    expect(await screen.findByText('Demo merchant')).toBeInTheDocument();
    expect(screen.getByText('ID: 12345')).toBeInTheDocument();
    expect(screen.getByText('Example Plan')).toBeInTheDocument();
    expect(screen.getByText(formatAmount(mockData.wallet_balance) + ' USD')).toBeInTheDocument();
    expect(screen.getByText(mockData.transactions_count)).toBeInTheDocument();
    expect(screen.getAllByText(mockData.pci_dss_level?.replace('level_', 'Level '))[0]).toBeInTheDocument();
    expect(screen.getByText('LOW')).toBeInTheDocument();
    expect(screen.getByText(getDateAndTime(mockData.date_created))).toBeInTheDocument();
  });

  test('Admin can enable issued cards access for an issuing merchant', async () => {
    const { user } = renderMockHolderDetails();

    const switchToggle = await screen.findByRole('switch', { name: /toggle switch/i });

    expect(switchToggle).toBeInTheDocument();

    await user.click(switchToggle);

    expect(await screen.findByRole('dialog', { hidden: true })).toBeInTheDocument();
    expect(screen.getByRole('heading', { name: 'Enable access to Issued Cards', hidden: true })).toBeInTheDocument();
    expect(screen.getByText(/you're about to enable access to issued cards \(customers\) for this merchant/i)).toBeInTheDocument();
    expect(screen.getByText(/once enabled, the merchant will regain full access to manage their customer cards/i)).toBeInTheDocument();

    const submitButton = screen.getByRole('button', { name: /enable/i, hidden: true });
    expect(submitButton).not.toBeDisabled();
    await user.click(submitButton);

    expect(await screen.findByText(/you have successfully enabled access to issued cards for this merchant\./i)).toBeInTheDocument();

    await user.click(screen.getByRole('button', { name: /dismiss/i, hidden: true }));
    expect(screen.queryByRole('dialog', { hidden: true })).not.toBeInTheDocument();
  });
  test('Admin can disable reserved cards access for an issuing merchant', async () => {
    const { user } = renderMockHolderDetails();

    const reservedTab = await screen.findByRole('tab', { name: /reserved virtual cards/i });

    expect(reservedTab).toBeInTheDocument();

    await user.click(reservedTab);

    const switchToggle = screen.getByRole('switch', { name: /toggle switch/i });

    expect(switchToggle).toBeInTheDocument();

    await user.click(switchToggle);

    expect(await screen.findByRole('dialog', { hidden: true })).toBeInTheDocument();

    expect(screen.getByRole('heading', { name: /disable access to reserved virtual cards/i, hidden: true })).toBeInTheDocument();
    expect(
      screen.getByText(
        /disabling rvcs will revoke this merchant's access to manage their rvcs. all active cards will be suspended immediately./i
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        /important: this means that this merchant won’t be able to issue any new cards and their reserved virtual cards will be suspended./i
      )
    ).toBeInTheDocument();

    const submitButton = screen.getByRole('button', { name: /disable$/i, hidden: true });
    expect(submitButton).not.toBeDisabled();

    await user.click(submitButton);
    expect(
      await screen.findByText(/you have successfully disabled access to reserved virtual cards for this merchant\./i)
    ).toBeInTheDocument();

    await user.click(screen.getByRole('button', { name: /dismiss/i, hidden: true }));
    expect(screen.queryByRole('dialog', { hidden: true })).not.toBeInTheDocument();
  });
});
