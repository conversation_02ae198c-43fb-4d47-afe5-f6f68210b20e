import { screen } from '@testing-library/react';

import { mockIssuingMerchantDetails } from '+mock/mockData';

import { renderMockHolderDetails } from './common';

describe('HolderDetails component', () => {
  test('Admin cannot switch partners when card access is inactive', async () => {
    renderMockHolderDetails();
    expect(await screen.findByRole('button', { name: 'Change Partner' })).toBeDisabled();
  });

  test('Admin can switch partners when card access is active', async () => {
    mockIssuingMerchantDetails.data.config.customer.status = 'active';

    const { user } = renderMockHolderDetails();

    await user.click(await screen.findByRole('button', { name: 'Change Partner' }));

    expect(await screen.findByRole('dialog', { hidden: true })).toBeInTheDocument();

    expect(screen.getByRole('heading', { name: /change issuing partner for this merchant/i, hidden: true }));
    expect(
      screen.getByText(
        /you are about to change the issuing partner for demo merchant. switching partners may impact how their customer cards are issued, managed, and any ongoing card operations/i
      )
    ).toBeInTheDocument();

    await user.selectOptions(screen.getByLabelText(/select issuing partner/i), 'paytrade');
    expect((screen.getByRole('option', { name: /paytrade/i, hidden: true }) as HTMLOptionElement).selected).toBe(true);

    await user.selectOptions(screen.getByLabelText(/Reason for changing/i), 'frequent_downtime');
    expect((screen.getByRole('option', { name: /frequent downtime/i, hidden: true }) as HTMLOptionElement).selected).toBe(true);

    const confirmButton = screen.getByRole('button', { name: /confirm and change/i, hidden: true });

    expect(confirmButton).not.toBeDisabled();

    await user.click(confirmButton);

    expect(
      await screen.findByRole('heading', {
        name: /confirm change of issuing partner/i,
        hidden: true
      })
    );

    expect(screen.getByText(/kindly confirm that you want to proceed with changing the customer cards issuing partner for demo merchant/i));

    expect(
      screen.getByText(
        /note that pending activities will continue to be handled by the current partner, while the new partner will manage new card activities/i
      )
    );

    const submitButton = screen.getByRole('button', { name: /yes, confirm/i, hidden: true });

    expect(confirmButton).not.toBeDisabled();

    await user.click(submitButton);

    expect(
      await screen.findByText(
        /you have successfully changed the customer cards issuing partner for demo merchant from passpoint to paytrade./i
      )
    ).toBeInTheDocument();

    await user.click(screen.getByRole('button', { name: /dismiss/i, hidden: true }));

    expect(screen.queryByRole('dialog', { hidden: true })).not.toBeInTheDocument();
    mockIssuingMerchantDetails.data.config.customer.status = 'inactive';
  });
});
