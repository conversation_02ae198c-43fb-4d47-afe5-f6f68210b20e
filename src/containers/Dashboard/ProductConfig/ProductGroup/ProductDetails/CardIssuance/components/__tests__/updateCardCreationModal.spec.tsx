import { screen } from '@testing-library/react';

import { mockIssuingMerchantDetails } from '+__mock__/mockData';

import { renderMockHolderDetails } from './common';

beforeAll(() => {
  mockIssuingMerchantDetails.data.config.customer.status = 'active';
});

afterAll(() => {
  mockIssuingMerchantDetails.data.config.customer.status = 'inactive';
});

const mockData = mockIssuingMerchantDetails.data;
describe('UpdateCardCreationModal component', () => {
  test('Admin can disable card creation for an issuing merchant', async () => {
    const { user } = renderMockHolderDetails();
    mockData.config.customer.card_creation_enabled = true;

    await user.click(await screen.findByRole('checkbox', { name: 'Turn off card creation for this merchant' }));

    await user.click(screen.getByRole('button', { name: 'Save' }));

    expect(await screen.findByRole('dialog', { hidden: true })).toBeInTheDocument();

    expect(screen.getByRole('heading', { name: 'Confirm changes?', hidden: true })).toBeInTheDocument();

    expect(
      screen.getByText(/please confirm if you want to turn off issued cards \(customers\) card creation for this merchant/i)
    ).toBeInTheDocument();

    expect(
      screen.getByText(
        /once you check the box, they will no longer be able to create cards for their customers unless the box is unchecked to re-enable it/i
      )
    ).toBeInTheDocument();

    const confirmButton = screen.getByRole('button', { name: /yes, confirm/i, hidden: true });

    expect(confirmButton).not.toBeDisabled();

    await user.click(confirmButton);
    expect(await screen.findByText(/you have successfully made changes to this configuration/i)).toBeInTheDocument();

    await user.click(screen.getByRole('button', { name: /dismiss/i, hidden: true }));
    expect(screen.queryByRole('dialog', { hidden: true })).not.toBeInTheDocument();
  });

  test('Admin can enable card creation for an issuing merchant', async () => {
    const { user } = renderMockHolderDetails();
    mockData.config.customer.card_creation_enabled = false;

    await user.click(await screen.findByRole('checkbox', { name: 'Turn off card creation for this merchant' }));

    await user.click(screen.getByRole('button', { name: 'Save' }));

    expect(await screen.findByRole('dialog', { hidden: true })).toBeInTheDocument();

    expect(screen.getByRole('heading', { name: 'Undo changes?', hidden: true })).toBeInTheDocument();

    expect(
      screen.getByText(/please confirm if you want to enable issued cards \(customers\) card creation for this merchant/i)
    ).toBeInTheDocument();

    expect(screen.getByText(/once you uncheck the box, they will be able to create cards for their customers again/i)).toBeInTheDocument();

    const confirmButton = screen.getByRole('button', { name: /yes, undo/i, hidden: true });

    expect(confirmButton).not.toBeDisabled();

    await user.click(confirmButton);
    expect(await screen.findByText(/you have successfully made changes to this configuration/i)).toBeInTheDocument();

    await user.click(screen.getByRole('button', { name: /dismiss/i, hidden: true }));
    expect(screen.queryByRole('dialog', { hidden: true })).not.toBeInTheDocument();
  });
});
