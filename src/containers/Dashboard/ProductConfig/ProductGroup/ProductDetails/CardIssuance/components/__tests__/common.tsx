import React from 'react';
import { render } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import MockIndexWithRoute from '+mock/MockIndexWithRoute';
import { isAllowed } from '+utils';

import HolderDetails from '../../HolderDetails';

vi.mock('+utils', async () => {
  const actualUtils = await vi.importActual('+utils');
  return {
    ...actualUtils,
    isAllowed: () => true
  };
});

export const renderMockHolderDetails = () => {
  const utils = render(
    <MockIndexWithRoute
      route="/dashboard/product-config/USD/card-issuance/:feature/holders/:merchantId"
      initialEntries={['/dashboard/product-config/USD/card-issuance/issued-cards/holders/123']}
    >
      <HolderDetails />
    </MockIndexWithRoute>
  );
  return {
    user: userEvent,
    ...utils
  };
};
