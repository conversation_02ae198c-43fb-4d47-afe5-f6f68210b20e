import React from 'react';
import { render } from '@testing-library/react';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';
import { CategoryType } from '+types/productConfig';

import RequestersTable from '../components/RequestersTable';

const MockedRequestersTable = ({ feature }: { feature: CategoryType }) => {
  return (
    <MockIndex>
      <RequestersTable feature={feature} />
    </MockIndex>
  );
};

describe('RequestersTable', () => {
  it('RequestersTable is accessible', async () => {
    const { container } = render(<MockedRequestersTable feature="issued-cards" />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
