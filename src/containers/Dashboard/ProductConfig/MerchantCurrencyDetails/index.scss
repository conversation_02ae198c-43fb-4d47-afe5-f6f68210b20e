@import 'styles/kpy-custom/_custom';
@import 'styles/kpy-custom/variables';

.top-row__container {
  margin-top: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.7rem;
  border-bottom: 1px solid #d5d8db;

  .__left {
    font-family: 'Averta PE';

    div:first-child {
      display: flex;

      p {
        margin-left: 0.5rem;
        color: #a9afbc;
        font-size: 15px;
        font-weight: 400;
        margin-bottom: 0.5rem;
      }
    }

    div:nth-child(2) {
      display: flex;
      align-items: center;
      column-gap: 1rem;
      margin-bottom: 0.5rem;

      h4 {
        color: #3e4b5b;
        font-size: 30px;
        font-weight: 600;
        margin: 0;
      }

      p {
        color: #a9afbc;
        font-size: 20px;
        font-weight: 500;
        margin: 0;
      }

      .status {
        max-width: 102px;
        max-height: 34px;
        font-size: 16px;
        font-weight: 500;
        padding: 2px 8px;
        margin-left: 0.5rem;
        border-radius: 0.2rem;
      }
    }

    &>.id-and-plan {
      display: flex;
      align-items: center;
      column-gap: 0.5rem;

      &>p {
        margin-bottom: 0;
      }

      span {
        height: 0.3rem;
        width: 0.3rem;
        border-radius: 50%;
        background-color: #a9afbc;
      }
    }
  }

  .__right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-end;

    p {
      margin-top: 0.8rem;
      color: #2376f3;
      font-size: 15px;
      font-weight: 400;
      margin: 0;
    }
  }
}

.detail-container {
  margin-top: 2.5rem;
  display: flex;
  column-gap: 4rem;
  margin-bottom: 3rem;

  @media (min-width: $breakpoint-desktop) {
    width: 95%;
  }

  article {
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      column-gap: 3rem;

      li {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        padding: 0.3rem 0rem;
        row-gap: 0.5rem;

        &:nth-child(2) {
          border-left: 1px solid #d5d8db;
          padding-left: 2rem;
        }

        span:last-child {
          color: #3e4b5b;
          font-size: 15px;
          font-weight: 600;
          margin: 0;
        }
      }
    }
  }

  .extra-details {
    display: flex;
    align-items: flex-end;
    padding-bottom: 0.3rem;
    gap: 1rem;

    .wrapper {
      display: flex;
      align-items: center;

      &:nth-child(2) {
        border-left: 1px solid #d5d8db;
        padding-left: 1rem;
      }

      .details-label {
        color: #4e555b;
        font-size: 15px;
        font-weight: 400;
        margin: 0;
        margin-right: 0.5rem;
      }
    }
  }
}
