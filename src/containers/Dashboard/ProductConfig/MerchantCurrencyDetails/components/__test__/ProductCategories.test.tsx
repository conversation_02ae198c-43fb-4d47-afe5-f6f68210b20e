import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';
import { Mock, vi } from 'vitest';

import { useSetUserAccess } from '+hooks';
import MockIndex from '+mock/MockIndex';
import { ProductConfigType } from '+types';

import ProductCategories from '../ProductCategories';

vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

const MockedProductCategories = ({ children }: { children: React.ReactNode }) => {
  return <MockIndex>{children}</MockIndex>;
};

const configData = {
  enabled: true,
  mobile_money: {
    channels: ['modal', 'web', 'api'],
    enabled: true,
    transaction_limit: { max: 10000000, min: 1000 }
  }
} as unknown as ProductConfigType;

describe('ProductCategories', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;
  mockUseSetUserAccess.mockReturnValue({ 'transaction_config_details.update': true });
  it('ProductCategories is accessible', async () => {
    const { container } = render(
      <MockedProductCategories>
        <ProductCategories type="pay-ins" merchantId="234" currency="NGN" config={configData} merchantsStatus />
      </MockedProductCategories>
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  it('ProductCategories should display empty state for product categories', () => {
    render(
      <MockedProductCategories>
        <ProductCategories
          type="pay-ins"
          merchantId="234"
          currency="NGN"
          config={{ enabled: true } as unknown as ProductConfigType}
          merchantsStatus
        />
      </MockedProductCategories>
    );
    expect(screen.getByText('Product Categories Not Available')).toBeInTheDocument();
  });
  it('ProductCategories should display empty state for product channels', () => {
    render(
      <MockedProductCategories>
        <ProductCategories
          type="pay-ins"
          merchantId="234"
          currency="NGN"
          config={
            {
              enabled: true,
              mobile_money: undefined
            } as unknown as ProductConfigType
          }
          merchantsStatus
        />
      </MockedProductCategories>
    );
    expect(screen.getByText('Channels Not Available')).toBeInTheDocument();
  });
  it('ProductCategories should display properly', async () => {
    render(
      <MockedProductCategories>
        <ProductCategories type="pay-ins" merchantId="234" currency="NGN" config={configData} merchantsStatus />
      </MockedProductCategories>
    );
    expect(screen.getByText('Product categories')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Here you can find the checkout and API products for this merchant’s Mobile Money configuration. You can modify these payment channels configuration here.'
      )
    ).toBeInTheDocument();
    expect(screen.queryByText('Payment via Dashboard:')).not.toBeInTheDocument();
    expect(screen.getByText('Payment via API:')).toBeInTheDocument();
    expect(screen.getAllByText('Enabled')).toHaveLength(2);
    expect(screen.getByText('Channels')).toBeInTheDocument();
    expect(screen.getByText('Transaction Limit')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Here you can find the limits for this merchant’s Mobile Money configuration. You can modify these limits configuration here.'
      )
    ).toBeInTheDocument();
    expect(screen.getByText('Maximum limit per transaction:')).toBeInTheDocument();
    expect(screen.getByText('Minimum limit per transaction:')).toBeInTheDocument();
    expect(screen.getByText('10,000,000.00')).toBeInTheDocument();
    expect(screen.getByText('1,000.00')).toBeInTheDocument();
    expect(screen.getByText('Mobile Money')).toBeInTheDocument();
    expect(screen.getByText('Allow this merchant to receive Mobile Money pay-ins in NGN')).toBeInTheDocument();
  });
  it('ProductCategories should not have Edit when permission is not granted', async () => {
    mockUseSetUserAccess.mockReturnValue({});
    render(
      <MockedProductCategories>
        <ProductCategories type="pay-ins" merchantId="234" currency="NGN" config={configData} merchantsStatus />
      </MockedProductCategories>
    );
    expect(screen.queryByText('Edit')).not.toBeInTheDocument();
  });
});
