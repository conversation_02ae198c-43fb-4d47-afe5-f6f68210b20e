import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';
import { Mock, vi } from 'vitest';

import EditDetailsCard from '+dashboard/ProductConfig/components/EditDetailsCard';
import { useSetUserAccess } from '+hooks';
import MockIndex from '+mock/MockIndex';

vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

const MockedEditDetailsCard = ({ children }: { children: React.ReactNode }) => {
  return <MockIndex>{children}</MockIndex>;
};

describe('EditDetailsCard', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;
  mockUseSetUserAccess.mockReturnValue({});
  it('EditDetails is accessible', async () => {
    const { container } = render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="channels"
          currency="NGN"
          category="pay-ins"
          paymentMethod="mobile_money"
          merchantId="23"
          content={['web', 'api']}
          type="Pay-ins"
          disableEdit={false}
        />
      </MockedEditDetailsCard>
    );
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  it('render correctly', () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="channels"
          currency="NGN"
          category="pay-ins"
          paymentMethod="mobile_money"
          merchantId="23"
          content={['web', 'api']}
          type="mobile_money"
          disableEdit={false}
        />
      </MockedEditDetailsCard>
    );
    expect(screen.getByText('Channels')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Here you can find the checkout and API products for this merchant’s Mobile Money configuration. You can modify these payment channels configuration here.'
      )
    ).toBeInTheDocument();
    expect(screen.queryByText('Payment via Dashboard:')).not.toBeInTheDocument();
    expect(screen.getByText('Payment via API:')).toBeInTheDocument();
    expect(screen.getAllByText('Enabled')).toHaveLength(1);
    expect(screen.getAllByText('Disabled')).toHaveLength(1);
  });
  it('render the available channels', () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="channels"
          currency="NGN"
          category="pay-ins"
          paymentMethod="mobile_money"
          merchantId="23"
          content={['web', 'api', 'modal']}
          type="mobile_money"
          disableEdit={false}
        />
      </MockedEditDetailsCard>
    );

    expect(screen.queryByText('Payment via Dashboard:')).not.toBeInTheDocument();
    expect(screen.getByText('Payment via API:')).toBeInTheDocument();
    expect(screen.getByText('Payment via Checkout:')).toBeInTheDocument();
    expect(screen.getAllByText('Enabled')).toHaveLength(2);
  });
  it('EditDetailsCard should not have Edit when permission is not  granted', async () => {
    mockUseSetUserAccess.mockReturnValue({ 'transaction_config_details.update': false });
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="channels"
          currency="NGN"
          category="pay-ins"
          paymentMethod="mobile_money"
          merchantId="23"
          content={['web', 'api', 'modal']}
          type="mobile_money"
          disableEdit
        />
      </MockedEditDetailsCard>
    );
    expect(screen.queryByText('Edit')).not.toBeInTheDocument();
  });
});
