import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import RiskIndicator from '+dashboard/ProductConfig/components/RiskIndicator';

describe('Risk Indicator', () => {
  it('Risk Indicator is accessible', async () => {
    const { container } = render(<RiskIndicator riskLevel="medium" />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  it('should render mid content when risk level is mid', () => {
    render(<RiskIndicator riskLevel="medium" />);
    expect(screen.getByText('MID')).toBeInTheDocument();
  });
  it('should render mid content when risk level is mid', () => {
    render(<RiskIndicator riskLevel="medium" />);
    expect(screen.getByText('MID')).toBeInTheDocument();
  });
  it('should render mid content when risk level is mid', () => {
    render(<RiskIndicator riskLevel="high" />);
    expect(screen.getByText('HIGH')).toBeInTheDocument();
  });
  it('should render mid content when risk level is mid', () => {
    render(<RiskIndicator riskLevel="low" />);
    expect(screen.getByText('LOW')).toBeInTheDocument();
  });
  it('should render mid content when risk level is mid', () => {
    render(<RiskIndicator riskLevel="above" />);
    expect(screen.getByText('ABOVE AVG')).toBeInTheDocument();
  });
});
