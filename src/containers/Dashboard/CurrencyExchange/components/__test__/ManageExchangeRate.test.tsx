import React from 'react';
import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';

import ManageExchangeRate from '../ManageExchangeRate';

const MockedManageExchangeRate = () => {
  return (
    <MockIndex>
      <ManageExchangeRate close={vi.fn()} />
    </MockIndex>
  );
};

describe('ManageExchangeRate modal', () => {
  test('ManageExchangeRate is accessible', async () => {
    const { container } = render(<MockedManageExchangeRate />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  test('Renders ManageExchangeRate', async () => {
    render(<MockedManageExchangeRate />);
    expect(screen.getByText(/Payment Channel/i)).toBeInTheDocument();
  });

  test('renders the ManageExchangeRate component', async () => {
    render(<MockedManageExchangeRate />);

    expect(screen.getByText(/Edit Kora Exchange Rate for USD/i)).toBeInTheDocument();
    expect(screen.getByTestId('second-button')).toBeInTheDocument();
  });

  test('changes the active tab when a tab button is clicked', async () => {
    render(<MockedManageExchangeRate />);
    const saveSettingsButton = screen.getByTestId('second-button');
    expect(saveSettingsButton).toBeInTheDocument();
    await act(async () => {
      userEvent.click(saveSettingsButton);
    });

    await waitFor(() => expect(screen.getByText(/Currency Pair/i)).toBeInTheDocument());
  });
});
