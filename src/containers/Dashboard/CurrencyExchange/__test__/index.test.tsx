import React from 'react';
import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';

import MockIndex from '+mock/MockIndex';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';
import { server } from '+mock/mockServers';

import CurrencyExchange from '../index';

const MockedExchange = () => {
  return (
    <MockIndex>
      <CurrencyExchange />
    </MockIndex>
  );
};
const MockedExchangeWithRoute = () => {
  return (
    <MockIndexWithRoute route="/dashboard/currency-exchange" initialEntries={['/dashboard/currency-exchange']}>
      <CurrencyExchange />
    </MockIndexWithRoute>
  );
};

describe('currency-exchange', () => {
  test('That the currency-exchange component is rendered', async () => {
    const { container } = render(<MockedExchange />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('Currency-exchange table is populated', async () => {
    render(<MockedExchangeWithRoute />);
    await waitFor(() => expect(screen.getAllByText('KPY-SWPcjsiprCOWi')).toHaveLength(1));
    await waitFor(() => expect(screen.getAllByText('KPY-SWPRNqj4xriDw')).toHaveLength(1));
    await waitFor(() => expect(screen.getAllByText('KPY-SWPiTRGvmIZ8t')).toHaveLength(1));
    await waitFor(() => expect(screen.getByText(/Manage Exchange Rate/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/Export Transaction/i)).toBeInTheDocument());
  });

  test('Exchange rate modal is render', async () => {
    server.use(
      http.get('/admin/cards', () => {
        return HttpResponse.json({ message: 'Successful' }, { status: 200 });
      })
    );
    const { getByText } = render(<MockedExchangeWithRoute />);
    const exportButton = screen.getByTestId('manage-exchange-rate-button');
    expect(exportButton).toBeInTheDocument();
    await act(async () => {
      userEvent.click(exportButton);
    });
    await waitFor(() => expect(getByText('Edit Kora Exchange Rate for USD')).toBeInTheDocument());
    await waitFor(() => expect(getByText('New Markup on Base Rate')).toBeInTheDocument());
  });
});
