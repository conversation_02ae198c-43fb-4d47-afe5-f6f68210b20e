.billing-config {
  --dark-50: hsla(220, 28%, 90%, 1);
  --dark-100: hsla(207, 20%, 65%, 1);
  --dark-150: hsla(204, 7%, 42%, 1);
  --dark-200: hsla(212, 19%, 31%, 1);
  --dark-250: hsla(200, 4%, 17%, 1);

  font-size: 0.9rem;
  color: var(--dark-200);

  & label {
    font-weight: 500;
  }

  & * {
    margin: 0;
  }

  &>*+* {
    margin-top: 2rem;
  }

  & .btn--ghost {
    gap: 0.5rem;
    background-color: transparent;
    border: 0;
    border-radius: 0.3rem;
    color: #2376f3;
    font-weight: 500;

    &:hover {
      color: #2376f3;
    }

    &:focus {
      outline: 1px solid #2376f3;
    }
  }

  &__header>*+*,
  &__summary>*+* {
    margin-top: 1rem;
  }

  &__title {
    font-size: 1.25rem;
  }

  &__description {
    color: var(--dark-100);
    max-width: 60ch;
    font-weight: 300;
  }

  &__tab-panel>*+* {
    margin-top: 2rem;
  }

  &__tab-panel-title {
    font-size: 1rem;
    color: var(--dark-150);
  }

  & .config-wrapper {
    padding-block: 1.5rem;
    padding-inline: 1.5rem;
    background-color: hsla(210, 50%, 98%, 1);
    border: 1px solid hsla(216, 91%, 91%, 1);

    &__heading>*+* {
      margin-top: 1rem;
    }

    &__title {
      font-size: 0.92rem;
    }

    &__description {
      max-width: 40ch;
    }

  }

  & .overlay-input {
    &__wrapper--outer>*+* {
      margin-top: 0.25em;
    }

    &__wrapper--inner {
      position: relative;
      font-size: 0.9rem;
      line-height: 1.5;
      color: #495057;

      &>input {
        padding-right: 2.5rem;

        &::placeholder {
          color: rgba(0, 0, 0, 0.4);
          font-weight: 300;
        }
      }

    }

    &__overlay {
      position: absolute;
      inset-block: 0;
      inset-inline-end: 0;
      padding: 0.5rem;
      display: flex;
      justify-content: center;
      align-items: center;
      color: rgba(65, 79, 95, 0.5);
      pointer-events: none;
    }
  }

  & .as-label {
    font-weight: 500;
  }
}
