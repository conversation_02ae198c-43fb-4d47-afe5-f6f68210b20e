import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { vi } from 'vitest';

import MockIndexWithRoute from '+mock/MockIndexWithRoute';
import { Storage } from '+services/storage-services';

import DashboardComponent from './index';

const dummyToken =
  '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

beforeAll(() => {
  // mocking jQuery $()
  const fakeQuery = vi.fn(() => {
    return {
      on: fakeQuery,
      off: fakeQuery,
      click: fakeQuery,
      slideUp: fakeQuery,
      slideDown: fakeQuery
    };
  });
  global.window.$ = fakeQuery;

  Storage.setItem('koraAdminUserToken', dummyToken);
});

function MockedDashboardWithRoute() {
  return (
    <MockIndexWithRoute route="/dashboard/virtual-accounts" initialEntries={['/dashboard/virtual-accounts', 'dashboard/home']}>
      <DashboardComponent location={{ pathname: '/dashboard/virtual-accounts' }} />
    </MockIndexWithRoute>
  );
}

describe('Dashboard index', () => {
  // Default tests for all components/pages

  test('Renders Dashboard index', async () => {
    render(<MockedDashboardWithRoute />);
    await waitFor(() => expect(screen.getAllByText('Home')[0]).toBeInTheDocument());
  });

  test('Dashboard text', async () => {
    // mobile-menu-trigger
    render(<MockedDashboardWithRoute />);

    const subHeader = screen.getByTestId('main-menu');
    await waitFor(() => expect(subHeader).toContainElement(screen.getByTestId('tools')));
    await waitFor(() => expect(screen.getByText('Korapay Technologies')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('Admin')).toBeInTheDocument());
  });
});
