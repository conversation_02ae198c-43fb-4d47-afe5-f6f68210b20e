@import 'styles/kpy-custom/variables';

:root {
  --gap-xs: 0.5rem;
  --gap-sm: 0.75rem;
  --gap-md: 1rem;
  --gap-lg: 1.25rem;
  --gap-xl: 1.5rem;
}

.cluster,
.with-sidebar,
.switcher {
  display: flex;
  gap: var(--gap-md);
  flex-wrap: wrap;
}

.nowrap {
  flex-wrap: nowrap;
}
.nogap {
  gap: 0;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.items-baseline {
  align-items: baseline;
}
.items-start {
  align-items: start;
}
.items-center {
  align-items: center;
}

.with-sidebar {
  --sidebar-size: 20rem;
  --breakpoint: 50%;

  > .sidebar {
    flex-basis: var(--sidebar-size);
    flex-grow: 1;
  }

  > .not-sidebar {
    flex-basis: 0;
    flex-grow: 999;
    min-inline-size: var(--breakpoint);
  }
}

.switcher {
  --threshold: 20rem;

  & > * {
    flex-grow: 1;
    flex-basis: calc((var(--threshold) - 100%) * 999);
  }
}

.gap {
  &-xs {
    gap: var(--gap-xs);
  }
  &-sm {
    gap: var(--gap-sm);
  }
  &-md {
    gap: var(--gap-md);
  }
  &-lg {
    gap: var(--gap-lg);
  }
  &-xl {
    gap: var(--gap-xl);
  }
}

.transaction-details-comp {
  > * {
    font-size: 0.875rem;
  }
  > * + * {
    margin-top: 3rem;
  }

  & ul {
    padding: 0;
    list-style: none;
  }

  & .go-back-btn {
    margin-top: -20px;
    padding: 0;
    font-weight: 500;
    font-size: 0.9rem;
    letter-spacing: -0.005em;
  }

  & .scroll-to-top .btn {
    font-weight: 500;
    letter-spacing: 0.086px;
    color: #414f5f;
    border-radius: 20px;
    background: #f9fbfd;
    box-shadow: 0 5px 7px rgba(126, 142, 177, 0.1);
  }

  & .copy-button > img {
    width: 0.9rem;
  }

  & .copyable {
    &-blue {
      font-weight: 500;
      color: #2376f3;
    }
    &-bold {
      font-weight: 500;
    }
  }

  & .card-logo {
    border-radius: 2px;
    width: 1.5rem;
    margin-right: 8px;
    box-shadow: 0px 1px 2px -1px rgb(0 0 0 / 30%);
  }

  .no-disputes-found {
    background-color: #f9fbfd;
    border-radius: 1rem;
    margin: 1rem;
    padding: 4rem;
    gap: 0.5rem;
    height: 100%;

    & > .image {
      width: 2.125rem;
      > img {
        display: block;
        max-width: 100%;
      }
    }

    & > .description {
      font-size: inherit;
      text-align: center;
      max-width: 20ch;
      color: #94a7b7;
    }
  }

  //  TRANSACTION LAYOUT TOP SECTION
  & .trxn-layout-top-section {
    margin-top: 1rem;
    > * + * {
      margin-top: 0.5rem;
    }
  }

  & .trxn-layout-heading {
    > * {
      margin: 0;
    }
    .heading-and-status {
      color: #c4c4c4;
    }
    .heading,
    .currency {
      font-weight: 600;
      text-transform: uppercase;
    }
    .heading {
      font-size: 1.5rem;
      color: #292b2c;
    }
    .currency {
      font-size: 1rem;
      color: #a9afbc;

      &:empty {
        display: none;
      }
    }
    .labels {
      display: flex;
      gap: 0.5rem;
    }
    .status {
      background: #f3f4f8;
      color: #94a7b7;
      padding: 0.125em 0.875em;
      border-radius: 5px;
      text-transform: capitalize;
      font-weight: 500;
    }

    & .trxn-layout-action-buttons {
      &:empty {
        display: none;
      }

      & .btn {
        gap: var(--gap-xs);
        font-size: inherit;
        font-weight: 500;
        border: none;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
      }
    }
  }

  // TRANSACTION LAYOUT SUMMARY
  & .trxn-layout-summary {
    border-top: 0.5px solid #a9afbc;
    padding-block: 1rem;

    @media (max-width: 768px) {
      display: block;
    }

    & > .summary-item + .summary-item {
      @media (max-width: 768px) {
        margin-top: 0.5rem;
        border-left: none;
        padding-left: 0;
      }

      border-left: 0.5px solid #a9afbc;
      padding-left: 0.75rem;
      margin-top: 0;
    }

    & > .summary-item:last-of-type {
      flex-grow: 1;
    }

    & > .summary-item:nth-child(4) {
      flex-grow: 4;
    }

    & > .summary-item {
      @media (max-width: 768px) {
        display: flex;
        flex-wrap: nowrap;
        gap: 1rem;
      }

      flex-grow: 2;
    }

    & .summary-label {
      font-weight: 300;

      @media (max-width: 768px) {
        max-width: 30ch;
        min-width: min(30%, 30ch);
      }
    }

    & .summary-value {
      @media (max-width: 768px) {
        text-align: right;
        min-width: 0;
        overflow-wrap: break-word;
        flex-grow: 1;
      }

      font-weight: 500;

      .merchant-name {
        background: none;
        border: none;
        font-weight: 500;
        color: #2376f3;
        padding: 0;
      }
    }
    & .btn[data-redirectto='original-transaction'] {
      color: #2376f3 !important;
      padding: 0;
      padding-top: 0.375rem;
      font-size: 0.85rem;
    }
  }

  // TRANSACTION LAYOUT SECTION
  & .trxn-layout-section {
    > * + * {
      margin-top: 0.25rem;
    }
    .section-title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .section-title__left {
        display: flex;
        align-items: center;
        gap: 0.5rem;

        p {
          font-weight: 500;
          color: #414f5f;
          margin: 0;
          font-size: 1rem;
        }

        button {
          background: none;
          border: none;
          font-weight: 500;
          color: $kpyblue;
          display: flex;
          align-items: center;

          img {
            width: 1rem;
            margin-left: 5px;
          }
        }
      }

      .view-breakdown {
        all: unset;
        color: #2376f3;
        cursor: pointer;
        font-size: 1rem;
        margin-left: auto;
        font-weight: 500;

        &:hover {
          transform: translate(-1%);
        }
      }
    }
    .section-list {
      border-top: 0.5px solid #a9afbc;
      padding-block: 1rem;
    }
    .section-item + .section-item {
      margin-top: 1rem;
    }
    .section-item-label {
      color: #a9afbc;
      font-weight: 500;
      flex-grow: 1;
      max-width: 30ch;
      min-width: min(30%, 30ch);
    }
    .section-item-value {
      flex-grow: 1;
      max-width: 70ch;

      @media (max-width: 768px) {
        text-align: right;
        min-width: 0;
        overflow-wrap: break-word;
      }
    }
  }

  // TRANSACTION DISPUTES SECTION
  & .trxn-layout-disputes {
    --sidebar-size: 17rem;
    --breakpoint: 65%;

    & > * + * {
      margin-top: 1rem;

      @media (min-width: 1145px) {
        margin-top: 0rem;
        border-left: 3px solid #f1f6fa;
      }
    }

    & .dispute-tabs {
      --threshold: 0;
      margin-bottom: 0;

      @media (min-width: 1145px) {
        --threshold: 25rem;
        gap: 0.5rem;
      }

      > * {
        flex-grow: 0.5;

        @media (min-width: 1145px) {
          flex-grow: 1;
          justify-content: flex-start;
        }
      }
    }

    & .dispute-tab {
      color: #414f5f;
      position: relative;
      padding-inline: 0;
      text-transform: capitalize;
      outline: initial;
      transition: all 250ms ease-in;
      border: none;
      background-color: transparent;

      @media (min-width: 1145px) {
        width: 100%;
        padding: 1em;
        text-align: left;
      }

      &:focus-visible {
        outline: auto;
      }

      &.active {
        color: #2376f3;
        @media (min-width: 1145px) {
          background-color: #f1f6fa;
        }
      }

      &.active::before {
        position: absolute;
        content: '';
        height: 3px;
        inset-inline: 0;
        top: 100%;
        background-color: #2376f3;
        border-radius: 8px;

        @media (min-width: 1145px) {
          inset-inline: auto;
          inset-inline-start: 0;
          inset-block: 0;
          width: 2px;
          height: auto;
        }
      }

      .tab-icon {
        display: none;

        @media (min-width: 1145px) {
          display: inline-block;
          border-top: 3px solid #a9afbc;
          border-right: 3px solid #a9afbc;
          width: 10px;
          height: 10px;
          transform: rotate(45deg);
        }

        &.active {
          border-color: #2376f3;
        }
      }
    }
  }
}

// TIMELINE CONTAINER
.timeline__container {
  position: relative;

  > * {
    font-size: 0.875rem;
  }

  &::before {
    position: absolute;
    content: '';
    width: 1px;
    top: 1rem;
    bottom: 1rem;
    transform: translateX(450%);
    background-color: #d5d8db;
  }
  .timeline-item {
    display: flex;
    gap: 1rem;
  }

  .timeline-item + .timeline-item {
    margin-top: 3rem;
  }

  .timeline__date {
    flex-grow: 1;
    max-width: 30ch;
    min-width: min(30%, 30ch);
    display: flex;
    align-items: center;
    gap: 15px;
    position: relative;
    z-index: 1;
    font-weight: 400;

    > img {
      width: 0.65rem;
    }
  }

  .timeline__description {
    flex-grow: 1;
    max-width: 70ch;
    color: #414f5f;

    @media (max-width: 768px) {
      text-align: right;
      min-width: 0;
      overflow-wrap: break-word;
    }
  }

  & + button {
    margin-top: 2rem;
    font-size: 14px;
    padding: 0.5rem 0.15rem 0.5rem 0;
    color: #2a7af0;
    display: flex;
    align-items: center;
    gap: 10px;

    > img {
      width: 1.125rem;
    }
  }
}

// CHARGEBACK DOCUMENTS
.chargeback-docs__container {
  border-radius: 10px;
  background-color: #f9fbfd;
  padding: 30px;
  margin-block-start: 1rem;

  > * {
    font-size: 0.875rem;
  }

  .chargeback-docs__list {
    padding-inline: 30px;
    padding-block: 20px;
    border-radius: 10px;
    background-color: #ffffff;
    box-shadow: 0 5px 3px 0 hsla(221, 25%, 59%, 0.04);

    > * {
      color: #94a7b7;
    }

    > p {
      font-weight: 400;
    }
    .chargeback-docs__item + .chargeback-docs__item {
      margin-top: 24px;
    }
  }

  .chargeback-docs__item {
    background-color: #f9fbfd;
    border: 3px solid #f3f4f8;
    padding-block: 10px;
    padding-inline: 15px;
    border-radius: 10px;
    font-size: 0.85rem;
    display: flex;
    gap: 20px;
    align-items: center;
    width: 100%;

    @media (max-width: 800px) {
      flex-direction: column;
      justify-content: center;
    }

    > .chargeback-docs__info {
      display: flex;
      flex-wrap: wrap;
      place-items: center;
      gap: 20px;

      @media (max-width: 800px) {
        flex-direction: column;
      }

      > svg path {
        fill: hsla(207, 20%, 65%, 1);
      }

      > span:nth-of-type(1) {
        font-weight: 500;
        min-width: 0;
        overflow-wrap: anywhere;
      }

      > span:nth-of-type(2) {
        font-weight: 400;
        color: #a9afbc;
      }
    }

    > .chargeback-docs__link {
      margin-inline-start: auto;
      color: #2376f3;
      display: flex;
      gap: 9px;
      min-width: 0;
      overflow-wrap: anywhere;

      @media (max-width: 800px) {
        margin-inline: auto;
      }
    }
  }
}
