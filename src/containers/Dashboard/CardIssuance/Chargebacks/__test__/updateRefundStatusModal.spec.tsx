/* eslint-disable react/jsx-props-no-spreading */
import React, { useState } from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';

import MockIndex from '+mock/MockIndex';
import { IUpdateRefundStatusModal } from '+types';

import UpdateRefundStatusModal from '../UpdateRefundStatusModal';

const defaultProps: Partial<IUpdateRefundStatusModal> = {
  currentStatus: 'pending',
  currency: 'USD',
  acceptedAmount: '8.00',
  refundId: 'demoReference'
};

const MockComponent = (props: Partial<IUpdateRefundStatusModal>) => {
  const [openModal, setOpenModal] = useState(false);

  return (
    <>
      <button type="button" onClick={() => setOpenModal(true)}>
        Open Modal
      </button>
      {openModal ? <UpdateRefundStatusModal {...defaultProps} {...props} onClose={() => setOpenModal(false)} /> : null}
    </>
  );
};

const setup = (props: Partial<IUpdateRefundStatusModal> = {}) => ({
  user: userEvent.setup(),
  ...render(
    <MockIndex>
      <MockComponent {...props} />
    </MockIndex>
  )
});

describe('Renders on trigger', () => {
  test("renders first step with 'Current fund status' dropdown disabled", async () => {
    const { user } = setup();

    await user.click(screen.getByRole('button', { name: /open modal/i }));
    expect(screen.getByText(/update status/i)).toBeInTheDocument();
    expect(screen.getByText(/fill in the details below to change the status of this refund/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/current refund status/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/current refund status/i)).toBeDisabled();
    expect(screen.getByLabelText(/new refund status/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/amount refunded/i)).toBeInTheDocument();
  });

  test('is accessible', async () => {
    const { user, container } = setup();

    user.click(screen.getByRole('button', { name: /open modal/i }));
    const result = await axe(container);
    expect(result).toHaveNoViolations();
  });
});

describe('Happy path', () => {
  test("status can be updated from 'pending' to 'partially_paid'", async () => {
    const { user } = setup();

    await user.click(screen.getByRole('button', { name: /open modal/i }));

    const currentRefundStatusField = screen.getByLabelText(/current refund status/i);
    const newRefundStatusField = screen.getByLabelText(/new refund status/i);
    const amountInput = screen.getByLabelText(/amount refunded/i);

    expect(currentRefundStatusField).not.toHaveValue('partially_paid');
    expect(currentRefundStatusField).toHaveValue('pending');

    await user.selectOptions(newRefundStatusField, 'partially_paid');
    expect(newRefundStatusField).toHaveValue('partially_paid');

    await user.type(amountInput, '800');
    expect(amountInput).toHaveValue('8.00');

    await user.click(screen.getByRole('button', { name: /next/i, hidden: true }));
    expect(await screen.findByLabelText(/yes, i understand the implications of this action/i)).toBeInTheDocument();
  });

  test("status can be updated from 'partially paid' to 'fully paid'", async () => {
    const { user } = setup({ currentStatus: 'partially_paid' });

    await user.click(screen.getByRole('button', { name: /open modal/i }));

    const currentRefundStatusField = screen.getByLabelText(/current refund status/i);
    const newRefundStatusField = screen.getByLabelText(/new refund status/i);
    const amountInput = screen.getByLabelText(/amount refunded/i);

    expect(currentRefundStatusField).not.toHaveValue('fully paid');
    expect(currentRefundStatusField).toHaveValue('partially_paid');

    await user.selectOptions(newRefundStatusField, 'fully_paid');
    expect((screen.getAllByRole('option', { name: /paid/i, hidden: true })[1] as HTMLOptionElement).selected).toBe(true);

    expect(amountInput).toBeDisabled();
    expect(amountInput).toHaveValue('8.00');

    await user.click(screen.getByRole('button', { name: /next/i, hidden: true }));
    expect(await screen.findByLabelText(/yes, i understand the implications of this action/i)).toBeInTheDocument();
  });
});
