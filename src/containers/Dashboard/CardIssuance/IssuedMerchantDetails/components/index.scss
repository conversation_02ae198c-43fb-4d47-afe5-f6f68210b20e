@import 'styles/kpy-custom/variables';

.modal-content {
    label {
        color: #414F5F;
        font-weight: 500;
    }

    #current-status {
        background: transparent !important;
    }

    .radio-form-group {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 0;
    
        .radio-container {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-top: -10px;
    
            .radio-input {
                display: flex;
                align-items: baseline;
                gap: 10px;
                margin-right: 20px;
    
                input {
                    margin-bottom: -10px;
                }
    
                label {
                    font-weight: 400;
                }
            }
        }
    }

    .form-container {
        max-height: 400px;
        overflow-y: auto;

        .fee-type {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            font-size: 0.875rem;
            font-weight: 600;

            button {
                background: none;
                border: none;
                color: #414F5F;
                cursor: pointer;
                display: flex;
                align-items: center;
                padding: 0;
                font-weight: 500;

                .indicator {
                    width: 6px;
                    height: 6px;
                    margin-right: 5px;
                    background: #CDE5FE;

                    &.active-indicator {
                        background: $kpyblue;
                    }
                }

                &.active {
                    color: $kpyblue;
                }


            }

            .dotted-line {
                width: 55%;
                border-bottom: 1px dashed #414F5F50;
                margin: 10px 0;
            }

            .active-dotted-line {
                width: 55%;
                border-bottom: 1px dashed $kpyblue;
                margin: 10px 0;
            }
        }

        .form-group {
            display: flex;
            flex-direction: column;
            margin-bottom: 1rem !important;

            .input-wrapper {
                position: relative;
                display: inline-block;

                .suffix {
                    position: absolute;
                    top: 50%;
                    right: 10px;
                    transform: translateY(-50%);
                    color: #414F5F50;
                }
            }
        }
    }

    .view-plan-filter {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20px 0;

        span {
            color: #414F5F;
            font-weight: 500;
        }

        .plan-filter {
            display: flex;
            border: $kpyblue 2px solid;
            border-radius: 6px;

            button {
                background: none;
                border: none;
                color: $kpyblue;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                padding: 5px 10px;        
            }

            button.active {
                background: $kpyblue;
                color: #fff;
                height: 100%;
            }
        }
    }

    .view-billing-detail {
        background: #F1F6FA;
        border-radius: 6px;
        padding: 17px;

        .billing-details {
            display: flex;
            justify-content: space-between;
            font-size: 14px;

            p {
                color: #01145450;
            }

            span {
                color: #A9AFBC;
            }
        }
    }
}