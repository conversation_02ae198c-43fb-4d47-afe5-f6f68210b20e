import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { Mock, vi } from 'vitest';

import { useSetUserAccess } from '+hooks';
import MockIndex from '+mock/MockIndex';

import PayOutMetricComponent from '../PayOutMetric';

const MockedPayOutMetricComponent = () => {
  return (
    <MockIndex>
      <PayOutMetricComponent />
    </MockIndex>
  );
};
vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

describe('Payout metric page', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;

  test('Payout metric page is render with correct props', async () => {
    mockUseSetUserAccess.mockReturnValue({ 'metabase.view': true });
    render(<MockedPayOutMetricComponent />);
    const iframe = await screen.findByTitle(/iframe/i);
    expect(iframe).toBeInTheDocument();
    expect(iframe).toHaveAttribute('src', 'https://iframe.payout');
    expect(iframe).toHaveAttribute('allow', 'fullscreen');
    expect(iframe).toHaveAttribute('sandbox', 'allow-scripts allow-same-origin');
    await waitFor(() => {
      expect(screen.queryByText(/No records yet/i)).not.toBeInTheDocument();
    });
    await waitFor(() => {
      expect(screen.queryByText(/There are no metrics data yet/i)).not.toBeInTheDocument();
    });
  });
  test('Payout metric page is render with correct props', async () => {
    mockUseSetUserAccess.mockReturnValue({ 'metabase.view': false });
    render(<MockedPayOutMetricComponent />);
    const noRecordsText = await screen.findByText(/No records yet/i);
    const noMetricsText = await screen.findByText(/There are no metrics data yet/i);
    expect(noRecordsText).toBeInTheDocument();
    expect(noMetricsText).toBeInTheDocument();
  });
});
