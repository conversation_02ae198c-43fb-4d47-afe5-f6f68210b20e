@import 'styles/kpy-custom/_custom';
@import 'styles/kpy-custom/variables';

.partners-balance {
  font-family: 'Averta PE';
  &__tabs {
    flex-direction: column-reverse;
  }

  @media (min-width: $breakpoint-desktop-sm) {
    &__tabs {
      flex-direction: row;
      align-items: center;
    }

    &__currency-switch {
      padding: 0.5rem 0;
      border-bottom: 1px solid #dee2e6;

      > div {
        float: none;
      }
    }
  }

  &__intro {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    width: 100%;

    div {
      p {
        max-width: 87%;
        margin-bottom: 0;
        color: #a9afbc;
      }
    }

    .btn-primary:hover {
      opacity: 0.8;
      background-color: #eaf2fe !important;
      border: none !important;
      color: #414f5f;
    }

    button {
      color: #414f5f !important;
      font-weight: 500;
      background-color: #eaf2fe !important;
      border: none;
      display: flex;
      gap: 0.5rem;
      width: 180px !important;

      @media (min-width: $breakpoint-desktop) {
        width: 200px;
        height: 40px;
        justify-content: center;
        align-items: center;
        font-size: 15.5px;
      }
    }

    @media (min-width: $breakpoint-desktop) {
      align-items: center;
    }

    i {
      font-weight: 600;
    }
  }

  &__summary {
    margin-top: 2rem;
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr));
    gap: 2rem;

    @media (min-width: $breakpoint-desktop) {
      margin-top: 5rem;
    }
  }

  .empty-state {
    margin-top: 2rem;
    width: 100%;
  }
}

.balance-summary-card {
  font-family: 'Averta PE';
  display: flex;
  flex-direction: column;

  &__bank {
    color: #414f5f;
    font-size: 13px;
    font-weight: 400;
    margin-bottom: 0;
  }
  &__amount {
    color: #292b2c;
    font-size: 24px;
    font-weight: 600;

    &.low {
      color: #f44336;
    }
  }
  &__time {
    color: #a9afbc;
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 0.6rem;
  }
  &__details {
    display: flex;
    column-gap: 0.5rem;
    align-items: center;
    color: #2376f3;
    font-size: 13px;
    font-weight: 400;
    cursor: pointer;
    text-decoration: none !important;

    &:hover {
      transform: translateY(-1px);
    }
    p {
      margin-bottom: 0;
    }
  }
}

.partners-balance-history {
  .table-section {
    margin-top: 3rem;
    .--blue-text {
      color: #2376f3;
      font-weight: 500;
      font-size: 14px;
    }
  }
  .first-section {
    display: flex;
    align-items: flex-start !important;
    justify-content: space-between;
    gap: 5rem;

    .title-wrapper {
      max-width: 60%;

      &.--loading {
        max-width: 30%;
      }
    }
    .controls {
      display: flex;
      justify-content: space-between;
      gap: 1rem;
    }

    button {
      font-weight: 500;
      display: flex;
      align-items: center;
      border-radius: 8px;
    }

    button:first-child {
      color: #414f5f;
      background-color: #eaf2fe;
      border: #eaf2fe;
      gap: 0.5rem;
      width: 180px !important;

      &:hover {
        opacity: 0.8;
        background-color: #eaf2fe !important;
        border: none !important;
        color: #414f5f;
      }

      @media (min-width: $breakpoint-desktop) {
        width: 200px;
        height: 40px;
        justify-content: center;
        align-items: center;
        font-size: 15.5px;
      }

      i {
        font-weight: 600;
      }
    }

    button:nth-child(2) {
      width: 120px !important;
      justify-content: center;
      gap: 0.5rem;

      i {
        font-weight: 600;
        font-size: 1rem;
      }
    }
  }
}

.select-option {
  width: 100%;
  padding: 3px 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;

  span {
    font-family: 'Averta PE';
  }

  span:nth-child(1) {
    color: #414f5f;
    font-weight: 400;
    font-size: 14px;
    max-width: 45%;
  }

  span:nth-child(2) {
    color: #a9afbc;
    font-weight: 500;
    font-size: 15px;
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

div[role='listbox'] .select-option.selected {
  span:nth-child(1),
  span:nth-child(2) {
    color: #ffffff !important;
  }
}

.form-group .form-control.is-invalid {
  border: 2px solid red;
}

.form-error {
  display: block;
}
