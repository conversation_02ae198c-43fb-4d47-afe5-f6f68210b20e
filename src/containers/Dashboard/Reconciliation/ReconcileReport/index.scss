@import 'styles/kpy-custom/_custom';
@import 'styles/kpy-custom/variables';

.recon-report {
  padding: 2rem 3rem;

  &__heading {
    margin-bottom: 3rem;

    &--title {
      margin-bottom: 1rem;
    }
  }

  &__content {
    &--card {
      display: flex;
      margin-block: 1.5rem;
      justify-content: space-between;

      &__left,
      &__right {
        flex: 1;
      }
    }

    &--empty {
      padding: 2rem 0;
      background: #f9fbfd;
      margin-block: 2rem;
    }

    &--field {
      border-width: 1px 0;
      border-style: solid;
      border-color: #dde2ec;
      border-bottom: none;
      padding: 1rem 0;
      display: flex;
      justify-content: space-between;
      column-gap: 2.5rem;
      @media (min-width: #{$breakpoint-desktop-l}) {
        column-gap: 3.5rem;
      }

      &__left,
      &__right {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        flex: 1;

        &--title {
          font-weight: 500;
          font-size: 0.9rem;
          margin: 0;
          color: #414f5f;
        }
      }
    }

    &--option {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }
    .border-dotted {
      border: 2px dashed #dde2ec;
      display: flex;
      align-items: center;
    }

    &--action {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      gap: 0.5rem;
      padding-top: 1rem;
      padding-left: 2rem;
      border-top: 1px solid #dde2ec;
      margin-top: 1.5rem;

      &__left {
        display: flex;
        flex-direction: row;
        gap: 0.5rem;
      }

      &__right {
        display: flex;
        flex-direction: row;
        gap: 0.5rem;

        .cancel-button {
          color: #f32345;
          font-weight: 600;
        }
      }
    }
  }

  &__preview {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.5rem;

    &--item {
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.8rem;
      color: #fff;
      font-weight: 500;
    }
  }
}

.reconciliation-option-row-wrapper {
  animation: slideInFromBottom 0.4s ease-out forwards;
  opacity: 0;
  position: relative;

  &.removing {
    animation: slideOutToBottom 0.3s ease-in forwards;
  }

  :global(.react-select__menu) {
    z-index: 9999 !important;
  }

  :global(.react-select__menu-portal) {
    z-index: 9999 !important;
  }
}

@keyframes slideInFromBottom {
  0% {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  1% {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  100% {
    opacity: 1;
    transform: none;
  }
}

@keyframes slideOutToBottom {
  0% {
    opacity: 1;
    transform: none;
  }
  100% {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
}
