import React, { useEffect, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useShallow } from 'zustand/react/shallow';

import useFeedbackHandler from '+hooks/useFeedbackHandler';
import APIRequest from '+services/api-services';
import EmptyStateComponent from '+shared/EmptyState';
import LoadingPlaceholder from '+shared/LoadingPlaceHolder';
import Modal from '+shared/Modal';
import Typography from '+shared/Typography';
import useReconciliationStore from '+store/reconciliationStore';
import { IProcessorReportMapping, ProcessorConfigDataType, ReconciliationDataType } from '+types';
import { capitalize, capitalizeFirst, history, logError } from '+utils';

import ReconciliationOptionRow from '../components/ReconciliationOptionRow';
import ReportProfileCard from '../components/ReportProfileCard';
import { buildReconciliationReportOptions } from '../helpers/reconcileReportHelper';

import './index.scss';

const apiRequest = new APIRequest();

export const generateRandomColor = () => {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

const ReconcileReport = () => {
  const uuid = crypto.randomUUID();
  const queryClient = useQueryClient();
  const { feedbackInit } = useFeedbackHandler();
  const { startReconciliationData, primaryKeyMappings, comparisonKeyMappings } = useReconciliationStore(
    useShallow(state => ({
      startReconciliationData: state.startReconciliationData,
      primaryKeyMappings: state.primaryKeyMappings,
      comparisonKeyMappings: state.comparisonKeyMappings
    }))
  );

  const setPrimaryKeyMappings = useReconciliationStore(state => state.setPrimaryKeyMappings);
  const setComparisonKeyMappings = useReconciliationStore(state => state.setComparisonKeyMappings);
  const updateComparisonKeyMappings = useReconciliationStore(state => state.updateComparisonKeyMappings);
  const deleteComparisonKeyMapping = useReconciliationStore(state => state.deleteComparisonKeyMapping);
  const clearStartReconciliationData = useReconciliationStore(state => state.clearStartReconciliationData);

  const [internalReportOptions, setInternalReportOptions] = useState<string[]>([]);
  const [processorReportOptions, setProcessorReportOptions] = useState<string[]>([]);
  const [displayPreview, setDisplayPreview] = useState(false);
  const [removingItems, setRemovingItems] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (!startReconciliationData.processor) {
      history.push('/dashboard/reconciliation/start');
    }
  }, []);

  useEffect(() => {
    if (internalReportOptions.length > 0 || processorReportOptions.length > 0) return;
    setInternalReportOptions(primaryKeyMappings.map(mapping => mapping.internal_report));
    setProcessorReportOptions(primaryKeyMappings.map(mapping => mapping.processor_report));
  }, [primaryKeyMappings]);

  const { isLoading } = useQuery(
    [`RECONCILIATION_PROCESSOR_CONFIG_${startReconciliationData.processor}_${startReconciliationData.payment_type}`],
    () => apiRequest.getSettlementReconciliationProcessorConfig(startReconciliationData.processor, startReconciliationData.payment_type),

    {
      refetchOnMount: 'always',
      onSuccess(data) {
        const actualData = data?.data.filter((item: ProcessorConfigDataType) => item.payment_type === startReconciliationData.payment_type);
        if (actualData.length > 0 && primaryKeyMappings.length === 0 && comparisonKeyMappings.length === 0) {
          const modifiedPrimaryKeyMappings = actualData[0].primary_key_mappings.map((mapping: IProcessorReportMapping) => ({
            ...mapping,
            color: generateRandomColor()
          }));
          setPrimaryKeyMappings(modifiedPrimaryKeyMappings);
          setComparisonKeyMappings(
            actualData[0].comparison_key_mappings.map((mapping: IProcessorReportMapping) => ({ ...mapping, id: uuid }))
          );
        }
      },
      enabled: !!startReconciliationData.processor
    }
  );

  const createReconciliation = useMutation((value: ReconciliationDataType) => apiRequest.createSettlementReconciliation(value), {
    onSuccess: () => {
      queryClient.invalidateQueries('RECONCILIATIONS');
    },
    onError: (error: { response: { data: { data: { amount: { message: string } }; message: string } } }) => {
      logError(error.response.data);
      feedbackInit({
        message: error.response.data?.message
          ? `${capitalizeFirst(error.response.data?.message)}`
          : 'There has been an error creating this reconciliation',
        type: 'danger'
      });
    }
  });

  const disableStartReconciliationButton = () => {
    if (comparisonKeyMappings.length === 0) return true;
    return comparisonKeyMappings.some(mapping => !mapping.internal_report || !mapping.processor_report);
  };
  const handleStartReconciliation = () => {
    createReconciliation.mutate({
      ...startReconciliationData,
      field_mapping: {
        processor: startReconciliationData.processor,
        payment_type: startReconciliationData.payment_type,
        primary_key_mappings: primaryKeyMappings.map(item => ({ ...item, color: undefined })),
        comparison_key_mappings: comparisonKeyMappings.map(item => ({ ...item, id: undefined }))
      }
    });
  };

  const handleAddNewColumn = () => {
    setComparisonKeyMappings([{ processor_report: '', internal_report: '', id: uuid }]);
  };

  const handleComparisonOptionChange = (value: string | number | (string | number)[], field: keyof IProcessorReportMapping, id: string) => {
    updateComparisonKeyMappings({ value: { [field]: value }, id });
  };

  const handleDelete = (id: string) => {
    setRemovingItems(prev => new Set(prev).add(id));

    setTimeout(() => {
      deleteComparisonKeyMapping(id);
      setRemovingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(id);
        return newSet;
      });
    }, 300);
  };

  const handlePreviewDisplay = () => {
    if (processorReportOptions.length === 0) return;
    setDisplayPreview(!displayPreview);
  };

  const handleCancel = () => {
    clearStartReconciliationData();
    history.push('/dashboard/reconciliation/start');
  };
  return (
    <section className="recon-report">
      <section className="recon-report__heading">
        <Typography variant="h2" className="recon-report__heading--title">
          Reconcile reports
        </Typography>
        <Typography variant="subtitle4" className="recon-report__heading--description">
          To start the reconciliation process, map each column from the processor’s report to their respective columns on Kora’s internal
          report and examine the discrepancies.
        </Typography>
      </section>
      <section className="recon-report__content">
        <div className="recon-report__content--card">
          <div className="recon-report__content--card__left">
            <ReportProfileCard
              processor={startReconciliationData.processor}
              numberOfColumns={processorReportOptions.length}
              label={capitalize(startReconciliationData.processor?.split('')[0])}
              displayPreview={handlePreviewDisplay}
              hidePreview={processorReportOptions.length === 0}
            />
          </div>
          <div className="recon-report__content--card__right">
            <ReportProfileCard hidePreview numberOfColumns={internalReportOptions.length} label={'K'} />
          </div>
        </div>
        {isLoading ? (
          <div>
            <LoadingPlaceholder type="text" content={3} />
            <LoadingPlaceholder type="text" content={3} />
          </div>
        ) : (
          <>
            {comparisonKeyMappings.length === 0 ? (
              <section className="recon-report__content--empty">
                <EmptyStateComponent heading="No reconciliation columns" message="Add a column to start reconciliation" />
              </section>
            ) : (
              <>
                <div className="recon-report__content--field">
                  <div className="recon-report__content--field__left">
                    <p className="recon-report__content--field__left--title">Columns detected on uploaded report</p>
                  </div>
                  <div className="recon-report__content--field__right">
                    <p className="recon-report__content--field__right--title">Column to be reconciled on Kora</p>
                  </div>
                </div>
                <section className="recon-report__content--option">
                  {comparisonKeyMappings.length > 0 &&
                    comparisonKeyMappings.map(item => {
                      const comparisonOptionWithoutCurrent = comparisonKeyMappings.filter(option => option.id !== item.id);
                      const isRemoving = removingItems.has(item.id);

                      return (
                        <div key={item.id} className={`reconciliation-option-row-wrapper ${isRemoving ? 'removing' : ''}`}>
                          <ReconciliationOptionRow
                            options={buildReconciliationReportOptions(primaryKeyMappings, comparisonOptionWithoutCurrent)}
                            value={item}
                            onChange={(value, field) => handleComparisonOptionChange(value, field, item.id)}
                            onDelete={() => handleDelete(item.id)}
                          />
                        </div>
                      );
                    })}
                </section>
              </>
            )}
            <button type="button" className="btn btn-light-blue border-dotted" onClick={handleAddNewColumn}>
              <i className="os-icon os-icon-plus mr-2 font-weight-bolder" />
              Add new column
            </button>
            <section className="recon-report__content--action">
              <div className="recon-report__content--action__left"></div>
              <div className="recon-report__content--action__right">
                <button type="button" className="cancel-button" onClick={handleCancel}>
                  Cancel
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={handleStartReconciliation}
                  disabled={disableStartReconciliationButton() || createReconciliation.isLoading}
                >
                  {createReconciliation.isLoading ? (
                    <span className="spinner-border spinner-border-sm" aria-hidden="true" />
                  ) : (
                    <>
                      Start Reconciliation
                      <i className="os-icon os-icon-arrow-right7 ml-2 font-weight-bolder" />
                    </>
                  )}
                </button>
              </div>
            </section>
          </>
        )}
      </section>
      <Modal
        visible={displayPreview}
        description={
          <div className="recon-report__preview">
            {processorReportOptions.map((item, index) => (
              <span
                key={item}
                className="recon-report__preview--item"
                style={{
                  backgroundColor: primaryKeyMappings[index]?.color
                }}
              >
                {item}
              </span>
            ))}
          </div>
        }
        heading="Processor Report Columns"
        close={() => handlePreviewDisplay()}
        firstButtonText="Close"
        firstButtonAction={() => handlePreviewDisplay()}
        hideSecondButton
        size="md"
        secondButtonColor="red"
      />
    </section>
  );
};

export default ReconcileReport;
