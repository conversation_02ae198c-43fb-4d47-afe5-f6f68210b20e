import React from 'react';

import Typography from '+shared/Typography';

import ReportProfileCard from '../components/ReportProfileCard';

import './index.scss';

const ReconcileReport = () => {
  return (
    <section className="recon-report">
      <section className="recon-report__heading">
        <Typography variant="h2" className="recon-report__heading--title">
          Reconcile reports
        </Typography>
        <Typography variant="subtitle4" className="recon-report__heading--description">
          To start the reconciliation process, map each column from the processor’s report to their respective columns on Kora’s internal
          report and examine the discrepancies.
        </Typography>
      </section>
      <section className="recon-report__content">
        <div className="recon-report__content--card">
          <div className="recon-report__content--card__left">
            <ReportProfileCard />
          </div>
          <div className="recon-report__content--card__right">
            <ReportProfileCard hidePreview />
          </div>
        </div>
        <div className="recon-report__content--field">
          <div className="recon-report__content--field__left">
            <p className="recon-report__content--field__left--title">Columns detected on uploaded report</p>
          </div>
          <div className="recon-report__content--field__right">
            <p className="recon-report__content--field__right--title">Column to be reconciled on Kora</p>
          </div>
        </div>
      </section>
    </section>
  );
};

export default ReconcileReport;
