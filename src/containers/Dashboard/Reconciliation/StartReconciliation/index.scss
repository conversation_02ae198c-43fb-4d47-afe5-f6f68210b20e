.start-reconciliation {
  padding-inline: 2rem;

  &__header {
    margin-bottom: 3rem;
  }

  &__content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    & > div {
      max-width: 450px;
    }

    &__header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-bottom: 1rem;
      border-bottom: 1px solid #e0e0e0;
      padding-bottom: 1rem;

      i {
        font-size: 2.5rem;
      }
      h5 {
        font-size: 20px;
        font-weight: 600;
        color: #000;
        margin-top: 0.8rem;
      }
      p {
        font-size: 13px;
        font-weight: 400;
        color: #94a7b7;
        margin-bottom: 0;
      }
    }

    &__form {
      display: flex;
      flex-direction: column;
      border-bottom: 1px solid #e0e0e0;
      padding-bottom: 0.5rem;

      .form-group {
        margin-bottom: 0.5rem;
      }

      & > div:nth-child(4) {
        label {
          color: #3e4b5b;
          font-weight: 500;
          font-size: 0.8rem;
        }
        p {
          margin-top: 0.5rem;
          margin-bottom: 0;
          color: #94a7b7;
          font-size: 12px;
          line-height: 1.5;
        }
      }
    }

    &__button {
      margin-top: 1.5rem;
      height: 40px;
      button {
        width: 100%;
        height: 100%;
      }
    }
  }

  &__inprogress {
    margin-top: 5rem;
    background-color: #f9fbfd;
    height: 60vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    padding: 2rem;

    .spinner {
      animation: rotation 10s infinite linear;
      width: 2rem;
      height: 2rem;
    }

    @keyframes rotation {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }

    p:nth-of-type(1) {
      font-size: 1.2rem;
      font-weight: 600;
      color: #000;
      margin-top: 1rem;
      margin-bottom: 0.5rem;
    }
    p:nth-of-type(2) {
      font-size: 0.9rem;
      font-weight: 400;
      color: #94a7b7;
    }

    &__loader {
      display: flex;
      flex-direction: row;
      gap: 0.5rem;
      margin-top: 1rem;
      padding: 0.5rem;
    }
  }
}
