import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { QueryClient, QueryClientProvider } from 'react-query';

import StartReconciliation from '../index';
import { mockProcessorData } from '+mock/mockData';

// Mock dependencies
vi.mock('+hooks', () => ({
  useFeedbackHandler: () => ({
    feedbackInit: vi.fn(),
    data: {
      statusCode: 200,
      isActive: false,
      isClosable: true,
      componentLevel: false,
      title: '',
      message: '',
      type: 'success' as const,
      action: undefined,
      callback: undefined
    },
    closeFeedback: vi.fn()
  })
}));

vi.mock('+utils', () => ({
  history: {
    goBack: vi.fn(),
    push: vi.fn()
  },
  logError: vi.fn()
}));

vi.mock('+store/reconciliationStore', () => ({
  __esModule: true,
  default: vi.fn((selector) => {
    const state = {
      setStartReconciliationData: vi.fn(),
      clearPrimaryKeyMappings: vi.fn(),
      clearComparisonKeyMappings: vi.fn()
    };
    return selector(state);
  })
}));

vi.mock('+services/api-services', () => {
  return {
    __esModule: true,
    default: vi.fn().mockImplementation(() => ({
      getProcessorList: vi.fn().mockResolvedValue(mockProcessorData),
      uploadSettlement: vi.fn().mockResolvedValue({ data: { success: true } }),
      generateUploadSettlementPresignedURL: vi.fn().mockResolvedValue({
        data: {
          key: 'test-key',
          dataValues: {
            id: 'test-id',
            path: 'https://test-presigned-url.com'
          }
        }
      }),
      // Add other required methods as no-ops
      setToken: vi.fn(),
      checkAuthToken: vi.fn(),
      clearToken: vi.fn(),
      instance: {},
      storeUserToken: vi.fn(),
      logout: vi.fn(),
      authorizeOauth: vi.fn(),
      completeOauth: vi.fn(),
      refresh: vi.fn()
    }))
  };
});

vi.mock('react-datepicker', () => ({
  __esModule: true,
  default: ({ onChange, selected, startDate, endDate, ...props }: any) => (
    <input
      data-testid="date-picker"
      value={selected ? selected.toISOString().split('T')[0] : ''}
      onChange={(e) => {
        const date = new Date(e.target.value);
        onChange([date, date]);
      }}
      {...props}
    />
  )
}));

vi.mock('+shared/FileUploader', () => ({
  __esModule: true,
  default: ({ onChange, clearUploads, uploading, uploaded, fileName, ...props }: any) => (
    <div data-testid="file-uploader">
      <input
        type="file"
        data-testid="file-input"
        onChange={onChange}
        {...props}
      />
      {uploading && <span data-testid="uploading">Uploading...</span>}
      {uploaded && <span data-testid="uploaded">Uploaded: {fileName}</span>}
      <button type="button" onClick={clearUploads} data-testid="clear-uploads">
        Clear
      </button>
    </div>
  )
}));

vi.mock('+shared/Modal', () => ({
  __esModule: true,
  default: ({ visible, close, firstButtonAction, secondButtonAction, heading, description, firstButtonText, secondButtonText }: any) =>
    visible ? (
      <div data-testid="modal">
        <h2>{heading}</h2>
        <div>{description}</div>
        <button onClick={firstButtonAction} data-testid="first-button">
          {firstButtonText}
        </button>
        <button onClick={secondButtonAction} data-testid="second-button">
          {secondButtonText}
        </button>
        <button onClick={close} data-testid="close-modal">Close</button>
      </div>
    ) : null
}));

vi.mock('+shared/ReactSelectDropdown', () => ({
  __esModule: true,
  default: ({ onChange, options, value, placeholder, label, ...props }: any) => (
    <div data-testid="react-select-dropdown">
      <label>{label}</label>
      <select
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        data-testid="select-input"
        {...props}
      >
        <option value="">{placeholder}</option>
        {options?.map((option: any) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  )
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false
      }
    }
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('StartReconciliation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should be accessible', async () => {
    const { container } = render(<StartReconciliation />, { wrapper: createWrapper() });
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should render the component with correct content', async () => {
    render(<StartReconciliation />, { wrapper: createWrapper() });

    expect(screen.getByText('Reconciliation Tool')).toBeInTheDocument();
    expect(screen.getByText('Fill the form below to start reconciliation process.')).toBeInTheDocument();
    expect(screen.getByText('Go Back')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
  });

  it('should render form fields correctly', async () => {
    render(<StartReconciliation />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(screen.getByText('Select Processor')).toBeInTheDocument();
    });

    expect(screen.getByText('Payment Type')).toBeInTheDocument();
    expect(screen.getByText('Report Date Range')).toBeInTheDocument();
    expect(screen.getByText('Upload record for reconciliation')).toBeInTheDocument();
  });

  it('should load processor options from API', async () => {
    render(<StartReconciliation />, { wrapper: createWrapper() });

    await waitFor(() => {
      const selectElement = screen.getByTestId('select-input');
      expect(selectElement).toBeInTheDocument();
    });

    // Check if processor options are loaded
    const selectElement = screen.getByTestId('select-input');
    expect(selectElement.children.length).toBeGreaterThan(1); // Should have placeholder + options
  });

  it('should handle processor selection', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(screen.getByTestId('select-input')).toBeInTheDocument();
    });

    const processorSelect = screen.getAllByTestId('select-input')[0];
    await user.selectOptions(processorSelect, 'korapay');

    expect(processorSelect).toHaveValue('korapay');
  });

  it('should handle payment type selection', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    const paymentTypeSelect = screen.getAllByTestId('select-input')[1];
    await user.selectOptions(paymentTypeSelect, 'payout');

    expect(paymentTypeSelect).toHaveValue('payout');
  });

  it('should handle date range selection', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    const datePicker = screen.getByTestId('date-picker');
    await user.type(datePicker, '2024-01-01');

    expect(datePicker).toHaveValue('2024-01-01');
  });

  it('should handle file upload', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    const fileInput = screen.getByTestId('file-input');
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });

    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(screen.getByTestId('uploading')).toBeInTheDocument();
    });
  });

  it('should disable continue button when form is incomplete', () => {
    render(<StartReconciliation />, { wrapper: createWrapper() });

    const continueButton = screen.getByRole('button', { name: 'Continue' });
    expect(continueButton).toBeDisabled();
  });

  it('should enable continue button when form is complete', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    // Wait for processor options to load
    await waitFor(() => {
      expect(screen.getAllByTestId('select-input')[0]).toBeInTheDocument();
    });

    // Fill out the form
    const processorSelect = screen.getAllByTestId('select-input')[0];
    await user.selectOptions(processorSelect, 'korapay');

    const paymentTypeSelect = screen.getAllByTestId('select-input')[1];
    await user.selectOptions(paymentTypeSelect, 'payout');

    const datePicker = screen.getByTestId('date-picker');
    await user.type(datePicker, '2024-01-01');

    const fileInput = screen.getByTestId('file-input');
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });
    await user.upload(fileInput, file);

    // Wait for file upload to complete
    await waitFor(() => {
      expect(screen.getByTestId('uploaded')).toBeInTheDocument();
    });

    const continueButton = screen.getByRole('button', { name: 'Continue' });
    expect(continueButton).not.toBeDisabled();
  });

  it('should handle go back button click', async () => {
    const user = userEvent.setup();
    const { history } = await import('+utils');

    render(<StartReconciliation />, { wrapper: createWrapper() });

    const goBackButton = screen.getByText('Go Back');
    await user.click(goBackButton);

    expect(history.goBack).toHaveBeenCalledTimes(1);
  });

  it('should show confirmation modal when going back with form data', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    // Wait for processor options to load
    await waitFor(() => {
      expect(screen.getAllByTestId('select-input')[0]).toBeInTheDocument();
    });

    // Fill some form data
    const processorSelect = screen.getAllByTestId('select-input')[0];
    await user.selectOptions(processorSelect, 'korapay');

    const goBackButton = screen.getByText('Go Back');
    await user.click(goBackButton);

    expect(screen.getByTestId('modal')).toBeInTheDocument();
    expect(screen.getByText('Go back?')).toBeInTheDocument();
  });

  it('should handle modal confirmation to go back', async () => {
    const user = userEvent.setup();
    const { history } = await import('+utils');

    render(<StartReconciliation />, { wrapper: createWrapper() });

    // Wait for processor options to load
    await waitFor(() => {
      expect(screen.getAllByTestId('select-input')[0]).toBeInTheDocument();
    });

    // Fill some form data
    const processorSelect = screen.getAllByTestId('select-input')[0];
    await user.selectOptions(processorSelect, 'korapay');

    const goBackButton = screen.getByText('Go Back');
    await user.click(goBackButton);

    const confirmButton = screen.getByTestId('second-button');
    await user.click(confirmButton);

    expect(history.goBack).toHaveBeenCalledTimes(1);
  });

  it('should handle modal cancellation', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    // Wait for processor options to load
    await waitFor(() => {
      expect(screen.getAllByTestId('select-input')[0]).toBeInTheDocument();
    });

    // Fill some form data
    const processorSelect = screen.getAllByTestId('select-input')[0];
    await user.selectOptions(processorSelect, 'korapay');

    const goBackButton = screen.getByText('Go Back');
    await user.click(goBackButton);

    const cancelButton = screen.getByTestId('first-button');
    await user.click(cancelButton);

    expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
  });

  it('should handle file upload error', async () => {
    const user = userEvent.setup();
    const mockAPIRequest = await import('+services/api-services');

    // Mock API to throw error
    vi.mocked(mockAPIRequest.default).mockImplementation(() => ({
      getProcessorList: vi.fn().mockResolvedValue(mockProcessorData),
      uploadSettlement: vi.fn().mockRejectedValue(new Error('Upload failed')),
      generateUploadSettlementPresignedURL: vi.fn().mockResolvedValue({
        data: {
          key: 'test-key',
          dataValues: {
            id: 'test-id',
            path: 'https://test-presigned-url.com'
          }
        }
      }),
      // Add other required methods as no-ops
      setToken: vi.fn(),
      checkAuthToken: vi.fn(),
      clearToken: vi.fn(),
      instance: {},
      storeUserToken: vi.fn(),
      logout: vi.fn(),
      authorizeOauth: vi.fn(),
      completeOauth: vi.fn(),
      refresh: vi.fn()
    } as any));

    render(<StartReconciliation />, { wrapper: createWrapper() });

    const fileInput = screen.getByTestId('file-input');
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });

    await user.upload(fileInput, file);

    // Should show uploading state
    await waitFor(() => {
      expect(screen.getByTestId('uploading')).toBeInTheDocument();
    });
  });

  it('should clear file upload', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    const fileInput = screen.getByTestId('file-input');
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });

    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(screen.getByTestId('uploading')).toBeInTheDocument();
    });

    const clearButton = screen.getByTestId('clear-uploads');
    await user.click(clearButton);

    expect(screen.queryByTestId('uploaded')).not.toBeInTheDocument();
  });

  it('should navigate to reconcile page on continue', async () => {
    const user = userEvent.setup();
    const { history } = await import('+utils');

    render(<StartReconciliation />, { wrapper: createWrapper() });

    // Wait for processor options to load
    await waitFor(() => {
      expect(screen.getAllByTestId('select-input')[0]).toBeInTheDocument();
    });

    // Fill out the form completely
    const processorSelect = screen.getAllByTestId('select-input')[0];
    await user.selectOptions(processorSelect, 'korapay');

    const paymentTypeSelect = screen.getAllByTestId('select-input')[1];
    await user.selectOptions(paymentTypeSelect, 'payout');

    const datePicker = screen.getByTestId('date-picker');
    await user.type(datePicker, '2024-01-01');

    const fileInput = screen.getByTestId('file-input');
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });
    await user.upload(fileInput, file);

    // Wait for file upload to complete
    await waitFor(() => {
      expect(screen.getByTestId('uploaded')).toBeInTheDocument();
    });

    const continueButton = screen.getByRole('button', { name: 'Continue' });
    await user.click(continueButton);

    expect(history.push).toHaveBeenCalledWith('/dashboard/reconciliation/reconcile');
  });

  it('should display correct payment type options', () => {
    render(<StartReconciliation />, { wrapper: createWrapper() });

    const paymentTypeSelect = screen.getAllByTestId('select-input')[1];

    expect(paymentTypeSelect).toBeInTheDocument();
    // Check if default value is 'payin'
    expect(paymentTypeSelect).toHaveValue('payin');
  });

  it('should handle API error for processor list', async () => {
    const mockAPIRequest = await import('+services/api-services');
    const mockFeedbackHandler = await import('+hooks');

    const mockFeedbackInit = vi.fn();
    vi.mocked(mockFeedbackHandler.useFeedbackHandler).mockReturnValue({
      feedbackInit: mockFeedbackInit,
      data: {
        statusCode: 200,
        isActive: false,
        isClosable: true,
        componentLevel: false,
        title: '',
        message: '',
        type: 'success' as const,
        action: undefined,
        callback: undefined
      },
      closeFeedback: vi.fn()
    });

    // Mock API to throw error
    vi.mocked(mockAPIRequest.default).mockImplementation(() => ({
      getProcessorList: vi.fn().mockRejectedValue(new Error('API Error')),
      uploadSettlement: vi.fn(),
      generateUploadSettlementPresignedURL: vi.fn(),
      // Add other required methods as no-ops
      setToken: vi.fn(),
      checkAuthToken: vi.fn(),
      clearToken: vi.fn(),
      instance: {},
      storeUserToken: vi.fn(),
      logout: vi.fn(),
      authorizeOauth: vi.fn(),
      completeOauth: vi.fn(),
      refresh: vi.fn()
    } as any));

    render(<StartReconciliation />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(mockFeedbackInit).toHaveBeenCalledWith({
        message: 'There has been an error fetching processors',
        type: 'danger',
        action: {
          action: expect.any(Function),
          name: 'Try again'
        }
      });
    });
  });

  it('should have correct CSS classes', () => {
    const { container } = render(<StartReconciliation />, { wrapper: createWrapper() });

    expect(container.querySelector('.element-box.start-reconciliation')).toBeInTheDocument();
    expect(container.querySelector('.start-reconciliation__header')).toBeInTheDocument();
    expect(container.querySelector('.start-reconciliation__content')).toBeInTheDocument();
  });

  it('should display file upload information text', () => {
    render(<StartReconciliation />, { wrapper: createWrapper() });

    expect(screen.getByText(/Please upload the record that you want to reconcile/)).toBeInTheDocument();
    expect(screen.getByText(/CSV/)).toBeInTheDocument();
    expect(screen.getByText(/XLSX/)).toBeInTheDocument();
    expect(screen.getByText(/10MB/)).toBeInTheDocument();
  });
});
