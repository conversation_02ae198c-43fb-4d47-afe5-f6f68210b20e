import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import StartReconciliation from '../index';

// Mock data
const mockProcessorData = {
  data: [
    { name: 'Korapay', slug: 'korapay' },
    { name: 'We<PERSON>', slug: 'wema' }
  ]
};

describe('StartReconciliation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should be accessible', async () => {
    const { container } = render(<StartReconciliation />, { wrapper: createWrapper() });
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should render the component with correct content', async () => {
    render(<StartReconciliation />, { wrapper: createWrapper() });

    expect(screen.getByText('Reconciliation Tool')).toBeInTheDocument();
    expect(screen.getByText('Fill the form below to start reconciliation process.')).toBeInTheDocument();
    expect(screen.getByText('Go Back')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Continue' })).toBeInTheDocument();
  });

  it('should render form fields correctly', async () => {
    render(<StartReconciliation />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(screen.getByText('Select Processor')).toBeInTheDocument();
    });

    expect(screen.getByText('Payment Type')).toBeInTheDocument();
    expect(screen.getByText('Report Date Range')).toBeInTheDocument();
    expect(screen.getByText('Upload record for reconciliation')).toBeInTheDocument();
  });

  it('should load processor options from API', async () => {
    render(<StartReconciliation />, { wrapper: createWrapper() });

    await waitFor(() => {
      const selectElement = screen.getByTestId('select-input');
      expect(selectElement).toBeInTheDocument();
    });

    // Check if processor options are loaded
    const selectElement = screen.getByTestId('select-input');
    expect(selectElement.children.length).toBeGreaterThan(1); // Should have placeholder + options
  });

  it('should handle processor selection', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(screen.getByTestId('select-input')).toBeInTheDocument();
    });

    const processorSelect = screen.getAllByTestId('select-input')[0];
    await user.selectOptions(processorSelect, 'korapay');

    expect(processorSelect).toHaveValue('korapay');
  });

  it('should handle payment type selection', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    const paymentTypeSelect = screen.getAllByTestId('select-input')[1];
    await user.selectOptions(paymentTypeSelect, 'payout');

    expect(paymentTypeSelect).toHaveValue('payout');
  });

  it('should handle date range selection', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    const datePicker = screen.getByTestId('date-picker');
    await user.type(datePicker, '2024-01-01');

    expect(datePicker).toHaveValue('2024-01-01');
  });

  it('should handle file upload', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    const fileInput = screen.getByTestId('file-input');
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });

    await user.upload(fileInput, file);

    await waitFor(() => {
      expect(screen.getByTestId('uploading')).toBeInTheDocument();
    });
  });

  it('should disable continue button when form is incomplete', () => {
    render(<StartReconciliation />, { wrapper: createWrapper() });

    const continueButton = screen.getByRole('button', { name: 'Continue' });
    expect(continueButton).toBeDisabled();
  });

  it('should enable continue button when form is complete', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    // Wait for processor options to load
    await waitFor(() => {
      expect(screen.getAllByTestId('select-input')[0]).toBeInTheDocument();
    });

    // Fill out the form
    const processorSelect = screen.getAllByTestId('select-input')[0];
    await user.selectOptions(processorSelect, 'korapay');

    const paymentTypeSelect = screen.getAllByTestId('select-input')[1];
    await user.selectOptions(paymentTypeSelect, 'payout');

    const datePicker = screen.getByTestId('date-picker');
    await user.type(datePicker, '2024-01-01');

    const fileInput = screen.getByTestId('file-input');
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });
    await user.upload(fileInput, file);

    // Wait for file upload to complete
    await waitFor(() => {
      expect(screen.getByTestId('uploaded')).toBeInTheDocument();
    });

    const continueButton = screen.getByRole('button', { name: 'Continue' });
    expect(continueButton).not.toBeDisabled();
  });

  it('should handle navigation and modal interactions', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    // Test direct navigation when no form data
    const goBackButton = screen.getByText('Go Back');
    await user.click(goBackButton);

    // Should not show modal initially (no form data)
    expect(screen.queryByTestId('modal')).not.toBeInTheDocument();

    // Wait for processor options to load and fill form
    await waitFor(() => {
      expect(screen.getAllByTestId('select-input')[0]).toBeInTheDocument();
    });

    const processorSelect = screen.getAllByTestId('select-input')[0];
    await user.selectOptions(processorSelect, 'korapay');

    // Now clicking back should show modal
    await user.click(goBackButton);
    expect(screen.getByTestId('modal')).toBeInTheDocument();
    expect(screen.getByText('Go back?')).toBeInTheDocument();

    // Test modal cancellation
    const cancelButton = screen.getByTestId('first-button');
    await user.click(cancelButton);
    expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
  });

  it('should handle file upload error', async () => {
    const user = userEvent.setup();
    const mockAPIRequest = await import('+services/api-services');

    // Mock API to throw error
    vi.mocked(mockAPIRequest.default).mockImplementation(
      () =>
        ({
          getProcessorList: vi.fn().mockResolvedValue(mockProcessorData),
          uploadSettlement: vi.fn().mockRejectedValue(new Error('Upload failed')),
          generateUploadSettlementPresignedURL: vi.fn().mockResolvedValue({
            data: {
              key: 'test-key',
              dataValues: {
                id: 'test-id',
                path: 'https://test-presigned-url.com'
              }
            }
          }),
          // Add other required methods as no-ops
          setToken: vi.fn(),
          checkAuthToken: vi.fn(),
          clearToken: vi.fn(),
          instance: {},
          storeUserToken: vi.fn(),
          logout: vi.fn(),
          authorizeOauth: vi.fn(),
          completeOauth: vi.fn(),
          refresh: vi.fn()
        }) as any
    );

    render(<StartReconciliation />, { wrapper: createWrapper() });

    const fileInput = screen.getByTestId('file-input');
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });

    await user.upload(fileInput, file);

    // Should show uploading state
    await waitFor(() => {
      expect(screen.getByTestId('uploading')).toBeInTheDocument();
    });
  });

  it('should handle file operations', async () => {
    const user = userEvent.setup();
    render(<StartReconciliation />, { wrapper: createWrapper() });

    const fileInput = screen.getByTestId('file-input');
    const file = new File(['test content'], 'test.csv', { type: 'text/csv' });

    // Test file upload
    await user.upload(fileInput, file);
    await waitFor(() => {
      expect(screen.getByTestId('uploading')).toBeInTheDocument();
    });

    // Test file clear
    const clearButton = screen.getByTestId('clear-uploads');
    await user.click(clearButton);
    expect(screen.queryByTestId('uploaded')).not.toBeInTheDocument();
  });

  it('should handle API error for processor list', async () => {
    const mockAPIRequest = await import('+services/api-services');
    const mockFeedbackHandler = await import('+hooks');

    const mockFeedbackInit = vi.fn();
    vi.mocked(mockFeedbackHandler.useFeedbackHandler).mockReturnValue({
      feedbackInit: mockFeedbackInit,
      data: {
        statusCode: 200,
        isActive: false,
        isClosable: true,
        componentLevel: false,
        title: '',
        message: '',
        type: 'success' as const,
        action: undefined,
        callback: undefined
      },
      closeFeedback: vi.fn()
    });

    // Mock API to throw error
    vi.mocked(mockAPIRequest.default).mockImplementation(
      () =>
        ({
          getProcessorList: vi.fn().mockRejectedValue(new Error('API Error')),
          uploadSettlement: vi.fn(),
          generateUploadSettlementPresignedURL: vi.fn(),
          // Add other required methods as no-ops
          setToken: vi.fn(),
          checkAuthToken: vi.fn(),
          clearToken: vi.fn(),
          instance: {},
          storeUserToken: vi.fn(),
          logout: vi.fn(),
          authorizeOauth: vi.fn(),
          completeOauth: vi.fn(),
          refresh: vi.fn()
        }) as any
    );

    render(<StartReconciliation />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(mockFeedbackInit).toHaveBeenCalledWith({
        message: 'There has been an error fetching processors',
        type: 'danger',
        action: {
          action: expect.any(Function),
          name: 'Try again'
        }
      });
    });
  });

  it('should have correct CSS classes', () => {
    const { container } = render(<StartReconciliation />, { wrapper: createWrapper() });

    expect(container.querySelector('.element-box.start-reconciliation')).toBeInTheDocument();
    expect(container.querySelector('.start-reconciliation__header')).toBeInTheDocument();
    expect(container.querySelector('.start-reconciliation__content')).toBeInTheDocument();
  });

  it('should display file upload information text', () => {
    render(<StartReconciliation />, { wrapper: createWrapper() });

    expect(screen.getByText(/Please upload the record that you want to reconcile/)).toBeInTheDocument();
    expect(screen.getByText(/CSV/)).toBeInTheDocument();
    expect(screen.getByText(/XLSX/)).toBeInTheDocument();
    expect(screen.getByText(/10MB/)).toBeInTheDocument();
  });
});
