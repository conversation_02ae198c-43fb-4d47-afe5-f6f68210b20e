import React, { useState } from 'react';
import DatePicker from 'react-datepicker';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import dayjs from 'dayjs';

import { useFeedbackHandler } from '+hooks';
import APIRequest from '+services/api-services';
import FileUploader from '+shared/FileUploader';
import LoadingPlaceholder from '+shared/LoadingPlaceHolder';
import ReactSelectDropdown from '+shared/ReactSelectDropdown';
import { ProcessorType, ReconciliationDataType } from '+types';
import { history, logError } from '+utils';

import calendar from '+assets/img/dashboard/calendar.svg';
import UploadFile from '+assets/img/dashboard/csv-file.svg';
import SpinnerIcon from '+assets/img/dashboard/spinner-blue.svg';

import 'react-datepicker/dist/react-datepicker.css';
import './index.scss';

const paymentTypeOptions = [
  {
    label: 'Pay-ins',
    value: 'payin'
  },
  {
    label: 'Pay-outs',
    value: 'payout'
  }
];

const apiRequest = new APIRequest();
const api = new APIRequest('https://api.koraapi.com/utilities');

const StartReconciliation = () => {
  const queryClient = useQueryClient();
  const { feedbackInit } = useFeedbackHandler();
  const [formData, setFormData] = useState<{
    processor: string;
    payment_type: 'payin' | 'payout';
    report_start_date: string | null;
    report_end_date: string | null;
    file: File | null;
  }>({
    processor: '',
    payment_type: 'payin',
    report_start_date: null,
    report_end_date: null,
    file: null
  });
  const [fileDetails, setFileDetails] = useState<{ key: string; id: string }>({ key: '', id: '' });

  const handleGoBack = () => {
    history.goBack();
  };

  const {
    data: processorOptions,
    refetch,
    isLoading
  } = useQuery([`PROCESSORLIST`], () => apiRequest.getProcessorList(), {
    keepPreviousData: true,
    refetchOnMount: 'always',
    select: (value: { data: ProcessorType[] }) => {
      const options = value.data.map(item => ({ label: item.name, value: item.slug }));
      return options;
    },
    onError: () => {
      feedbackInit({
        message: `There has been an error fetching processors`,
        type: 'danger',
        action: {
          action: () => {
            refetch();
          },
          name: 'Try again'
        }
      });
    }
  });

  const createReconciliation = useMutation((value: ReconciliationDataType) => apiRequest.createSettlementReconciliation(value), {
    onSuccess: data => {
      queryClient.invalidateQueries('RECONCILIATIONS');
    },
    onError: (error: { response: { data: { data: { amount: { message: string } }; message: string } } }) => {
      logError(error);

      feedbackInit({
        message: 'There has been an error funding this creating reconciliation',
        type: 'danger',
        componentLevel: true
      });
    }
  });

  const uploadSettlementFile = useMutation(
    (data: { presigned_url: string }) =>
      apiRequest.uploadSettlement({
        presignedURL: data.presigned_url,
        file: formData.file
      }),
    {
      onError: (error: { response: { data: { message: string } } }) => {
        logError(error);
        feedbackInit({
          message: error.response.data.message || 'There has been an error uploading the settlement file',
          type: 'danger',
          componentLevel: true
        });
      }
    }
  );

  const generatePresignedURL = useMutation(
    (value: string) =>
      api.generateUploadSettlementPresignedURL({
        file_name: value,
        bucket_name: process.env.REACT_APP_SETTLEMENT_FILE_BUCKET_NAME ?? '',
        category: 'settlement_file'
      }),
    {
      onSuccess: data => {
        if (data?.data?.dataValues?.path) {
          setFileDetails({
            key: data.data?.key,
            id: data.data?.dataValues.id
          });
          uploadSettlementFile.mutate({
            presigned_url: data.data?.dataValues.path
          });
        }
      },
      onError: (error: { response: { data: { message: string } } }) => {
        logError(error);
        feedbackInit({
          message: error.response.data.message || 'There has been an error generating the presigned URL for the settlement file',
          type: 'danger',
          componentLevel: true
        });
      }
    }
  );

  const validateForm = () => {
    return Boolean(
      formData.processor &&
        formData.payment_type &&
        formData.report_start_date &&
        formData.report_end_date &&
        fileDetails.id &&
        fileDetails.key
    );
  };

  const handleNavigateToSummary = () => {
    createReconciliation.mutate({
      processor: formData.processor,
      report_start_date: formData.report_start_date ?? '',
      report_end_date: formData.report_end_date ?? '',
      payment_type: formData.payment_type,
      processor_file_id: String(fileDetails.id),
      processor_file_details: {
        key: fileDetails.key
      }
    });
  };

  return (
    <section className="element-box start-reconciliation">
      {!createReconciliation.isLoading ? (
        <>
          <div className="row start-reconciliation__header">
            <div className="col-sm-12">
              <button type="button" className="btn btn-link" onClick={handleGoBack}>
                <i className="os-icon os-icon-arrow-left7" />
                <span style={{ fontWeight: 500 }}>Go Back</span>
              </button>
            </div>
          </div>
          <div className=" start-reconciliation__content">
            <div className="col-lg-6">
              <div className="start-reconciliation__content__header">
                <span>
                  <i className="os-icon os-icon-command " />
                </span>
                <h5>Reconciliation Tool</h5>
                <p>Fill the form below to start reconciliation process.</p>
              </div>
              <div className="start-reconciliation__content__form">
                {!isLoading && (
                  <div className="form-group">
                    <ReactSelectDropdown
                      label="Select Processor"
                      onChange={value => setFormData({ ...formData, processor: value as string })}
                      placeholder="Which processor’s record do you want to reconcile?"
                      options={processorOptions || []}
                      value={formData?.processor}
                    />
                  </div>
                )}
                <div className="form-group">
                  <ReactSelectDropdown
                    label="Payment Type"
                    onChange={value => setFormData({ ...formData, payment_type: value as 'payin' | 'payout' })}
                    placeholder="What type of payments do you want to reconcile?"
                    options={paymentTypeOptions}
                    value={formData?.payment_type}
                  />
                </div>
                <div className="form-group" style={{ '--calendar-image': `url(${calendar})` } as React.CSSProperties}>
                  <label htmlFor="deadline" className="withdraw-label">
                    <span className="dark">Report Date Range</span>
                  </label>
                  <DatePicker
                    selected={formData.report_start_date ? new Date(formData.report_start_date) : new Date()}
                    onChange={dates => {
                      const [start, end] = dates;
                      setFormData({
                        ...formData,
                        report_start_date: start ? dayjs(start).format('YYYY-MM-DD') : null,
                        report_end_date: end ? dayjs(end).format('YYYY-MM-DD') : null
                      });
                    }}
                    selectsRange
                    startDate={formData.report_start_date ? new Date(formData.report_start_date) : null}
                    endDate={formData.report_end_date ? new Date(formData.report_end_date) : null}
                    dateFormat="dd/MM/yyyy"
                    calendarClassName="custom-datepicker"
                    className="date-input"
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="file-uploader">Upload record for reconciliation</label>
                  <FileUploader
                    accept=".csv, .xlsx"
                    onChange={value => {
                      setFormData({ ...formData, file: value.target.files?.[0] ?? null });
                      generatePresignedURL.mutate(value.target.files?.[0]?.name ?? '');
                    }}
                    id="file-uploader"
                    text="Drop your file here or click to browse"
                    infoText=""
                    uploading={uploadSettlementFile.isLoading || generatePresignedURL.isLoading}
                    uploaded={Boolean(fileDetails.id)}
                    fileName={formData?.file?.name ?? ''}
                    clearUploads={() => {
                      setFormData({ ...formData, file: null });
                      setFileDetails({key: '', id: ''});
                    }}
                    maxFileSizeMb={10}
                    uploadedIcon={UploadFile}
                  />
                  <p className="upload-info">
                    Please upload the record that you want to reconcile. Ensure that the file format matches the requirements of the
                    selected processor. The record should remain in its raw, unedited form. The uploaded file must be in{' '}
                    <strong>.CSV</strong> or <strong>.XLSX</strong> format and should not exceed 10MB in size.
                  </p>
                </div>
              </div>
              <div className="start-reconciliation__content__button">
                <button className="btn btn-primary" type="button" onClick={handleNavigateToSummary} disabled={!validateForm() || createReconciliation.isLoading}>
                  <span>Continue</span>
                </button>
              </div>
            </div>
          </div>
        </>
      ) : (
        <section className="start-reconciliation__inprogress">
          <img alt="SpinnerIcon" src={SpinnerIcon} className="spinner" role="status" aria-hidden="true" />
          <p>Your file is being processed...</p>
          <p>This may take a few minutes.</p>
          <div className="start-reconciliation__inprogress__loader">
            <LoadingPlaceholder type="text" content={3} />
            <LoadingPlaceholder type="text" content={3} />
            <LoadingPlaceholder type="text" content={3} />
          </div>
        </section>
      )}
    </section>
  );
};

export default StartReconciliation;
