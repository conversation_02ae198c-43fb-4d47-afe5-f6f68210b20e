import React from 'react';

import Icon from '+shared/Icons';
import ReactSelectDropdown from '+shared/ReactSelectDropdown';
import { IProcessorReportMapping } from '+types';

type optionT = {
  label: string;
  value: string;
};

const ReconciliationOptionRow = ({
  options,
  value,
  onChange,
  onDelete
}: {
  options: optionT[][];
  value: IProcessorReportMapping;
  onChange: (value: string | number | (string | number)[], field: keyof IProcessorReportMapping) => void;
  onDelete: () => void;
}) => {
  return (
    <div className="reconciliation-option-row">
      <div className="option-group">
        <div className="option-group__left">
          <ReactSelectDropdown
            options={options[0]}
            placeholder={`Select option`}
            label=""
            value={value.processor_report}
            onChange={value => onChange(value, 'processor_report')}
          />
        </div>
        <span>- Match with -</span>
        <div className="option-group__right">
          <ReactSelectDropdown
            options={options[1]}
            placeholder={`Select option`}
            label=""
            value={value.internal_report}
            onChange={value => onChange(value, 'internal_report')}
          />
        </div>
        <div onKeyDown={onDelete} tabIndex={0} onClick={onDelete} role="button">
          <Icon name="trashIcon" />
        </div>
      </div>
    </div>
  );
};

export default ReconciliationOptionRow;
