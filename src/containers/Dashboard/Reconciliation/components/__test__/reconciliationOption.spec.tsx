import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import { IProcessorReportMapping } from '+types';

import ReconciliationOptionRow from '../ReconciliationOptionRow';

describe('ReconciliationOptionRow', () => {
  const mockOptions = [
    [
      { label: 'Transaction ID', value: 'transaction_id' },
      { label: 'Amount', value: 'amount' },
      { label: 'Date', value: 'date' }
    ],
    [
      { label: 'Internal ID', value: 'internal_id' },
      { label: 'Payment Amount', value: 'payment_amount' },
      { label: 'Created Date', value: 'created_date' }
    ]
  ];

  const mockValue: IProcessorReportMapping = {
    processor_report: 'transaction_id',
    internal_report: 'internal_id'
  };

  const mockOnChange = vi.fn();
  const mockOnDelete = vi.fn();

  const defaultProps = {
    options: mockOptions,
    value: mockValue,
    onChange: mockOnChange,
    onDelete: mockOnDelete
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('should be accessible', async () => {
    const { container } = render(<ReconciliationOptionRow {...defaultProps} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  test('renders component with correct dropdowns and delete button', () => {
    render(<ReconciliationOptionRow {...defaultProps} />);

    // Check for the dropdowns
    const dropdowns = screen.getAllByRole('combobox');
    expect(dropdowns).toHaveLength(2);

    // Check for match text
    expect(screen.getByText('- Match with -')).toBeInTheDocument();

    // Check for delete button
    const deleteButton = screen.getByRole('button');
    expect(deleteButton).toBeInTheDocument();
  });

  test('displays selected values correctly', () => {
    render(<ReconciliationOptionRow {...defaultProps} />);

    // Check if selected values are displayed
    expect(screen.getByDisplayValue('transaction_id')).toBeInTheDocument();
    expect(screen.getByDisplayValue('internal_id')).toBeInTheDocument();
  });

  test('calls onChange when processor report dropdown changes', async () => {
    render(<ReconciliationOptionRow {...defaultProps} />);

    const processorDropdown = screen.getAllByRole('combobox')[0];
    await userEvent.selectOptions(processorDropdown, 'amount');

    expect(mockOnChange).toHaveBeenCalledWith('amount', 'processor_report');
  });

  test('calls onChange when internal report dropdown changes', async () => {
    render(<ReconciliationOptionRow {...defaultProps} />);

    const internalDropdown = screen.getAllByRole('combobox')[1];
    await userEvent.selectOptions(internalDropdown, 'payment_amount');

    expect(mockOnChange).toHaveBeenCalledWith('payment_amount', 'internal_report');
  });

  test('calls onDelete when delete button is clicked', async () => {
    render(<ReconciliationOptionRow {...defaultProps} />);

    const deleteButton = screen.getByRole('button');
    await userEvent.click(deleteButton);

    expect(mockOnDelete).toHaveBeenCalledTimes(1);
  });

  test('calls onDelete when Enter key is pressed on delete button', () => {
    render(<ReconciliationOptionRow {...defaultProps} />);

    const deleteButton = screen.getByRole('button');
    fireEvent.keyDown(deleteButton, { key: 'Enter', code: 'Enter' });

    expect(mockOnDelete).toHaveBeenCalledTimes(1);
  });

  test('renders with empty initial values', () => {
    const emptyValue: IProcessorReportMapping = {
      processor_report: '',
      internal_report: ''
    };

    render(<ReconciliationOptionRow {...defaultProps} value={emptyValue} />);

    const dropdowns = screen.getAllByRole('combobox');
    expect(dropdowns[0]).toHaveValue('');
    expect(dropdowns[1]).toHaveValue('');
  });

  test('renders with different options', () => {
    const customOptions = [
      [
        { label: 'Custom Field 1', value: 'custom_1' },
        { label: 'Custom Field 2', value: 'custom_2' }
      ],
      [
        { label: 'Internal Field 1', value: 'internal_1' },
        { label: 'Internal Field 2', value: 'internal_2' }
      ]
    ];

    render(<ReconciliationOptionRow {...defaultProps} options={customOptions} />);

    // Verify custom options are available
    const dropdowns = screen.getAllByRole('combobox');
    expect(dropdowns[0].children).toHaveLength(2);
    expect(dropdowns[1].children).toHaveLength(2);
  });
});
