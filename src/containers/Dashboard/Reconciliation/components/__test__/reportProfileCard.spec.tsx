import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import ReportProfileCard from '../ReportProfileCard';

describe('ReportProfileCard', () => {
  const mockDisplayPreview = vi.fn();

  const defaultProps = {
    label: 'R',
    processor: 'wema',
    numberOfColumns: 5,
    displayPreview: mockDisplayPreview
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should be accessible', async () => {
    const { container } = render(<ReportProfileCard {...defaultProps} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should render the card with correct content', () => {
    render(<ReportProfileCard {...defaultProps} />);

    expect(screen.getByText('R')).toBeInTheDocument();
    expect(screen.getByText('Uploaded Report (Wema)')).toBeInTheDocument();
    expect(screen.getByText('5 columns detected')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Preview' })).toBeInTheDocument();
  });

  it('should render without processor name when processor is not provided', () => {
    const propsWithoutProcessor = { ...defaultProps, processor: undefined };
    render(<ReportProfileCard {...propsWithoutProcessor} />);

    expect(screen.getByText('Uploaded Report')).toBeInTheDocument();
    expect(screen.queryByText('(Wema)')).not.toBeInTheDocument();
  });

  it('should render with different label', () => {
    const propsWithDifferentLabel = { ...defaultProps, label: 'CSV' };
    render(<ReportProfileCard {...propsWithDifferentLabel} />);

    expect(screen.getByText('CSV')).toBeInTheDocument();
  });

  it('should display correct number of columns', () => {
    const propsWithDifferentColumns = { ...defaultProps, numberOfColumns: 10 };
    render(<ReportProfileCard {...propsWithDifferentColumns} />);

    expect(screen.getByText('10 columns detected')).toBeInTheDocument();
  });

  it('should call displayPreview when preview button is clicked', async () => {
    render(<ReportProfileCard {...defaultProps} />);

    const previewButton = screen.getByRole('button', { name: 'Preview' });
    await userEvent.click(previewButton);

    expect(mockDisplayPreview).toHaveBeenCalledTimes(1);
  });

  it('should not render preview button when hidePreview is true', () => {
    const propsWithHidePreview = { ...defaultProps, hidePreview: true };
    render(<ReportProfileCard {...propsWithHidePreview} />);

    expect(screen.queryByRole('button', { name: 'Preview' })).not.toBeInTheDocument();
    expect(screen.getByText('5 columns detected')).toBeInTheDocument();
  });

  it('should render without preview button when displayPreview is not provided', () => {
    const propsWithoutDisplayPreview = { ...defaultProps, displayPreview: undefined };
    render(<ReportProfileCard {...propsWithoutDisplayPreview} />);

    expect(screen.queryByRole('button', { name: 'Preview' })).not.toBeInTheDocument();
  });

  it('should have correct CSS classes', () => {
    const { container } = render(<ReportProfileCard {...defaultProps} />);

    expect(container.querySelector('.report-profile-card')).toBeInTheDocument();
    expect(container.querySelector('.report-profile-card__image')).toBeInTheDocument();
    expect(container.querySelector('.report-profile-card__content')).toBeInTheDocument();
    expect(container.querySelector('.report-profile-card__content--title')).toBeInTheDocument();
  });

  it('should handle zero columns', () => {
    const propsWithZeroColumns = { ...defaultProps, numberOfColumns: 0 };
    render(<ReportProfileCard {...propsWithZeroColumns} />);

    expect(screen.getByText('0 columns detected')).toBeInTheDocument();
  });

  it('should handle undefined numberOfColumns', () => {
    const propsWithUndefinedColumns = { ...defaultProps, numberOfColumns: undefined };
    render(<ReportProfileCard {...propsWithUndefinedColumns} />);

    expect(screen.getByText('undefined columns detected')).toBeInTheDocument();
  });

  it('should capitalize processor name correctly', () => {
    const propsWithLowercaseProcessor = { ...defaultProps, processor: 'sterling' };
    render(<ReportProfileCard {...propsWithLowercaseProcessor} />);

    expect(screen.getByText('Uploaded Report (Sterling)')).toBeInTheDocument();
  });

  it('should handle empty label', () => {
    const propsWithEmptyLabel = { ...defaultProps, label: '' };
    const { container } = render(<ReportProfileCard {...propsWithEmptyLabel} />);

    const imageElement = container.querySelector('.report-profile-card__image');
    expect(imageElement).toBeInTheDocument();

    const labelElement = imageElement?.querySelector('p');
    expect(labelElement).toBeInTheDocument();
    expect(labelElement).toBeEmptyDOMElement();
  });

  it('should have accessible preview button', () => {
    render(<ReportProfileCard {...defaultProps} />);

    const previewButton = screen.getByRole('button', { name: 'Preview' });
    expect(previewButton).toBeInTheDocument();

    expect(previewButton.tagName).toBe('BUTTON');
  });

  it('should have correct component structure', () => {
    const { container } = render(<ReportProfileCard {...defaultProps} />);

    const card = container.querySelector('.report-profile-card');
    const image = card?.querySelector('.report-profile-card__image');
    const content = card?.querySelector('.report-profile-card__content');
    const title = content?.querySelector('.report-profile-card__content--title');

    expect(card).toBeInTheDocument();
    expect(image).toBeInTheDocument();
    expect(content).toBeInTheDocument();
    expect(title).toBeInTheDocument();
  });
});
