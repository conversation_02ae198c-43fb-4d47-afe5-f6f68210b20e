import { fireEvent, render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';

import { ReportProfileCard } from '../ReportProfileCard';

describe('ReportProfileCard', () => {
  const mockProps = {
    title: 'Test Report',
    subTitle: 'Report Subtitle',
    date: '2023-07-15',
    icon: 'chart',
    onClick: vi.fn(),
    isActive: false
  };

  it('renders the card with correct content', () => {
    render(<ReportProfileCard {...mockProps} />);

    expect(screen.getByText(mockProps.title)).toBeInTheDocument();
    expect(screen.getByText(mockProps.subTitle)).toBeInTheDocument();
    expect(screen.getByText(mockProps.date)).toBeInTheDocument();
  });

  it('calls onClick when card is clicked', () => {
    render(<ReportProfileCard {...mockProps} />);

    const card = screen.getByText(mockProps.title).closest('div');
    fireEvent.click(card);

    expect(mockProps.onClick).toHaveBeenCalledTimes(1);
  });

  it('applies active styling when isActive is true', () => {
    const activeProps = { ...mockProps, isActive: true };
    const { rerender } = render(<ReportProfileCard {...activeProps} />);

    const card = screen.getByText(mockProps.title).closest('div');
    expect(card).toHaveClass('active'); // Assuming 'active' class is applied

    // Re-render with isActive as false
    rerender(<ReportProfileCard {...mockProps} />);
    expect(card).not.toHaveClass('active');
  });

  it('displays the correct icon', () => {
    render(<ReportProfileCard {...mockProps} />);

    const iconElement = screen.getByTestId('report-icon'); // Assuming there's a data-testid
    expect(iconElement).toBeInTheDocument();
  });
});
