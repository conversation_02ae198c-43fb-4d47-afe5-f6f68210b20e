import React from 'react';

import { capitalize } from '+utils';

import './index.scss';

const ReportProfileCard = ({
  hidePreview,
  processor,
  numberOfColumns,
  label,
  displayPreview
}: {
  hidePreview?: boolean;
  processor?: string;
  numberOfColumns?: number;
  label: string;
  displayPreview?: () => void;
}) => {
  return (
    <div className="report-profile-card">
      <div className="report-profile-card__image">
        <p>{label}</p>
      </div>
      <div className="report-profile-card__content">
        <p className="report-profile-card__content--title">Uploaded Report {processor && `(${capitalize(processor)})`}</p>
        <span>
          <p>{`${numberOfColumns} columns detected`}</p>
          {hidePreview || !displayPreview ? null : (
            <>
              <span />
              <button onClick={displayPreview}  >
                Preview</button>
            </>
          )}
        </span>
      </div>
    </div>
  );
};

export default ReportProfileCard;
