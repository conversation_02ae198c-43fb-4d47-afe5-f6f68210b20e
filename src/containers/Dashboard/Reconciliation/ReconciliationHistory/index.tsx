import React from 'react';
import { useHistory } from 'react-router-dom';

import EmptyStateComponent from '+shared/EmptyState';
import Typography from '+shared/Typography';

import './index.scss';

import ReconciliationHistoryTable from '+dashboard/Reconciliation/ReconciliationHistory/ReconciliationHistoryTable';

const ReconciliationHistory = () => {
  const history = useHistory();

  const handleStartReconciliation = () => {
    history.push('/dashboard/reconciliation/start');
  };

  return (
    <section className="recon-container">
      <section className="recon-container__heading">
        <div className="recon-container__heading--left">
          <Typography variant="h2" className="recon-container__heading--left__title">
            Reconcile reports between Kora and Processors.
          </Typography>
          <Typography variant="subtitle4" className="recon-container__heading--left__description">
            This application enables you to check and match transaction details between Kora and Processors. It enables you identify and
            rectify errors or unpaid transactions, ensuring that the records of both parties are accurate.
          </Typography>
        </div>
        <div className="recon-container__heading--right">
          <button type="button" className="btn btn-primary" onClick={handleStartReconciliation}>
            <i className="os-icon os-icon-plus" />
            New Reconciliation
          </button>
        </div>
      </section>
      {false ? (
        <section className="recon-container__content">
          <EmptyStateComponent
            heading="No reconciled files yet"
            message="It looks like you have not created any reconciliation at the moment."
            actionButton={
              <button
                type="button"
                className="btn btn-link mt-5 font-weight-bold text-decoration-none text-xl pointer"
                onClick={handleStartReconciliation}
              >
                <i className="os-icon os-icon-plus mr-2 mb-1" />
                Start reconciliation
              </button>
            }
          />
        </section>
      ) : (
        <ReconciliationHistoryTable />
      )}
    </section>
  );
};

export default ReconciliationHistory;
