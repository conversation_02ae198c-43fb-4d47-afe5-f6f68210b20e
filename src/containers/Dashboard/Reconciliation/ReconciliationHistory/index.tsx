import React from 'react';
import { useQuery } from 'react-query';
import { useHistory } from 'react-router-dom';

import Table from '+containers/Dashboard/Shared/Table';
import { useFeedbackHandler, useSearchQuery, useSetUserAccess } from '+hooks';
import APIRequest from '+services/api-services';
import EmptyStateComponent from '+shared/EmptyState';
import Typography from '+shared/Typography';
import { SortingParamsType } from '+types';
import { filteredOutObjectProperty, isAllowed, logError, queriesParams } from '+utils';

import { reconciliationHistory } from '../helpers/reconciliationHistoryHelper';

import './index.scss';

const api = new APIRequest();

const ReconciliationHistory = () => {
  const history = useHistory();
  const userAccess = useSetUserAccess();
  const { feedbackInit } = useFeedbackHandler();
  const searchQuery = useSearchQuery<{
    page: string;
    limit: string;
    dateFrom: string;
    dateTo: string;
    status: string;
  }>();

  const page = searchQuery.value.page ?? '1';
  const limit = searchQuery.value.limit ?? 10;
  const status = searchQuery.value.status ?? undefined;

  const sortingParams = {
    status,
    ...filteredOutObjectProperty(searchQuery.value, [
      queriesParams.tab,
      queriesParams.page,
      queriesParams.limit,
      queriesParams.dateFrom,
      queriesParams.dateTo,
      queriesParams.status,
      'currency'
    ])
  } satisfies SortingParamsType;

  const { data, refetch, isFetching } = useQuery(
    ['RECONCILIATIONS', limit, page, sortingParams],
    () => api.getSettlementReconciliations({ page, limit: Number(limit ?? 1), sortingParams }),
    {
      refetchOnMount: 'always',
      onError: error => {
        logError(error);
        feedbackInit({
          message: `There has been an error in getting this partner's balance history`,
          type: 'danger',
          action: {
            action: () => refetch(),
            name: 'Try again'
          }
        });
      }
    }
  );

  const handleStartReconciliation = () => {
    history.push('/dashboard/reconciliation/start');
  };

  return (
    <section className="recon-container">
      <section className="recon-container__heading">
        <div className="recon-container__heading--left">
          <Typography variant="h2" className="recon-container__heading--left__title">
            Reconcile reports between Kora and Processors.
          </Typography>
          <Typography variant="subtitle4" className="recon-container__heading--left__description">
            This application enables you to check and match transaction details between Kora and Processors. It enables you identify and
            rectify errors or unpaid transactions, ensuring that the records of both parties are accurate.
          </Typography>
        </div>
        {isAllowed(userAccess, ['settlement_reconciliations.process']) && (
          <div className="recon-container__heading--right">
            <button type="button" className="btn btn-primary" onClick={handleStartReconciliation}>
              <i className="os-icon os-icon-plus mr-2 font-weight-bolder" />
              New Reconciliation
            </button>
          </div>
        )}
      </section>
      {data && data?.data.length === 0 ? (
        <section className="recon-container__content">
          <EmptyStateComponent
            heading="No reconciled files yet"
            message="It looks like you have not created any reconciliation at the moment."
            actionButton={
              isAllowed(userAccess, ['settlement_reconciliations.process']) && (
                <button
                  type="button"
                  className="btn btn-link mt-5 font-weight-bold text-decoration-none text-xl pointer"
                  onClick={handleStartReconciliation}
                >
                  <i className="os-icon os-icon-plus mr-2 mb-1 font-weight-bolder" />
                  Start reconciliation
                </button>
              )
            }
          />
        </section>
      ) : (
        <section className="recon-container__table">
          {data && data?.data.length !== 0 && (
            <div className="recon-container__table--info">
              <p>Reconciled reports are available to download for 7 days, after which they are deleted permanently.</p>
            </div>
          )}
          <Table
            annotation="Reconciliation History"
            className={reconciliationHistory.className}
            data={data?.data ?? []}
            tableHeadings={['Status', 'Reconciled Report', 'Date Created', 'Action']}
            loading={isFetching}
            renderFields
            hasPagination
            pageSize={data?.paging?.page_size ?? Number(limit)}
            totalItems={data?.paging?.total_items}
            current={parseInt(page, 10)}
            hideTable={data?.data?.length === 0}
            tableWrapperClassName="table-responsive table-wrapper"
            emptyStateHeading={reconciliationHistory?.emptyStateHeading || ''}
            emptyStateMessage={reconciliationHistory.emptyStateMessage || ''}
            actionFn={currentPage => searchQuery.setQuery({ page: String(currentPage) })}
            limitAction={currentLimit => searchQuery.setQuery({ limit: String(currentLimit) })}
            filterHasAdvancedFilter={false}
            type="reconciliation"
            filterShowExport={false}
            hasFilter={false}
          >
            {reconciliationHistory.fields}
          </Table>
        </section>
      )}
    </section>
  );
};

export default ReconciliationHistory;
