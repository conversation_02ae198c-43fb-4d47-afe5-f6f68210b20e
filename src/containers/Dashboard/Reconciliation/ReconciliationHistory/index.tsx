import React from 'react';
import { useHistory } from 'react-router-dom';

import { useFeedbackHand<PERSON>, useSearchQuery } from '+hooks';
import APIRequest from '+services/api-services';
import EmptyStateComponent from '+shared/EmptyState';
import Typography from '+shared/Typography';
import { filteredOutObjectProperty, getPresentDate, logError, queriesParams } from '+utils';

import ReconciliationHistoryTable from './ReconciliationHistoryTable';

import './index.scss';

import { useQuery } from 'react-query';

import { SortingParamsType } from '+types';

const api = new APIRequest();

const ReconciliationHistory = () => {
  const history = useHistory();
  const { feedbackInit } = useFeedbackHandler();
  const searchQuery = useSearchQuery<{
    page: string;
    limit: string;
    dateFrom: string;
    dateTo: string;
    status: string;
  }>();

  const page = searchQuery.value.page ?? '1';
  const limit = searchQuery.value.limit ?? 10;
  const status = searchQuery.value.status ?? undefined;
  const dateFrom = searchQuery.value.dateFrom ?? getPresentDate().dateFrom;
  const dateTo = searchQuery.value.dateTo ?? getPresentDate().dateTo;

  const sortingParams = {
    status,
    date_from: dateFrom,
    date_to: dateTo,
    ...filteredOutObjectProperty(searchQuery.value, [
      queriesParams.tab,
      queriesParams.page,
      queriesParams.limit,
      queriesParams.dateFrom,
      queriesParams.dateTo,
      queriesParams.status,
      'currency'
    ])
  } satisfies SortingParamsType;

  const { data, refetch, isFetching } = useQuery(
    ["RECONCILIATIONS", limit, page, sortingParams],
    () => api.getSettlementReconciliations(page, Number(limit ?? 1), sortingParams),
    {
      refetchOnMount: 'always',
      onError: error => {
        logError(error);
        feedbackInit({
          message: `There has been an error in getting this partner's balance history`,
          type: 'danger',
          action: {
            action: () => refetch(),
            name: 'Try again'
          }
        });
      }
    }
  );
  console.log(data, 'data');
  const handleStartReconciliation = () => {
    history.push('/dashboard/reconciliation/start');
  };

  return (
    <section className="recon-container">
      <section className="recon-container__heading">
        <div className="recon-container__heading--left">
          <Typography variant="h2" className="recon-container__heading--left__title">
            Reconcile reports between Kora and Processors.
          </Typography>
          <Typography variant="subtitle4" className="recon-container__heading--left__description">
            This application enables you to check and match transaction details between Kora and Processors. It enables you identify and
            rectify errors or unpaid transactions, ensuring that the records of both parties are accurate.
          </Typography>
        </div>
        <div className="recon-container__heading--right">
          <button type="button" className="btn btn-primary" onClick={handleStartReconciliation}>
            <i className="os-icon os-icon-plus" />
            New Reconciliation
          </button>
        </div>
      </section>
      {false ? (
        <section className="recon-container__content">
          <EmptyStateComponent
            heading="No reconciled files yet"
            message="It looks like you have not created any reconciliation at the moment."
            actionButton={
              <button
                type="button"
                className="btn btn-link mt-5 font-weight-bold text-decoration-none text-xl pointer"
                onClick={handleStartReconciliation}
              >
                <i className="os-icon os-icon-plus mr-2 mb-1" />
                Start reconciliation
              </button>
            }
          />
        </section>
      ) : (
        <ReconciliationHistoryTable />
      )}
    </section>
  );
};

export default ReconciliationHistory;
