import React from 'react';

import { useSearchQuery } from '+hooks';
import Table from '+shared/Table';
import { ReconciliationHistoryType } from '+types';
import { capitalizeRemovedash, getDate, getTime, switchStatus } from '+utils';

import './index.scss';

import Icon from '+shared/Icons';

const data = {
  data: {
    data: [
      {
        id: 1,
        status: 'pending',
        description: 'Wema - Kora Report (8/Mar/2025 - 8/Apr/2025)',
        created_at: '2025-03-08T12:00:00.000Z'
      },
      {
        id: 2,
        status: 'processing',
        description: 'Wema - Kora Report (8/Mar/2025 - 8/Apr/2025)',
        created_at: '2025-03-08T12:00:00.000Z'
      },
      {
        id: 3,
        status: 'successful',
        description: 'Providus - Kora Report (10/Dec/2024 - 10/Feb/2025)',
        created_at: '2025-03-08T12:00:00.000Z'
      },
      {
        id: 5,
        status: 'successful',
        description: 'Mastercard - Kora Report (6/Oct/2024 - 6/Dec/2024)',
        created_at: '2025-04-20T12:00:00.000Z'
      }
    ],
    paging: {
      total_items: 4,
      total_pages: 1,
      current_page: 1,
      next_page: 2,
      previous_page: 1,
      has_next_page: true,
      has_previous_page: false,
      per_page: 10,
      from: 1
    }
  }
};

const ReconciliationHistoryTable = () => {
  const searchQuery = useSearchQuery();
  const limit = searchQuery.value.limit ?? '10';
  const page = searchQuery.value.page ?? '1';

  const reconciliationHistory = {
    className: '--reconciliation-history-table',
    emptyStateHeading: 'No History yet',
    data: data?.data?.data || [],
    emptyStateMessage: 'Reconciliation History Not Available',
    fields: (each: ReconciliationHistoryType) => ({
      data: {
        status: (
          <>
            <span className={`status-pill smaller ${switchStatus(each.status)}`} />
            <span>{capitalizeRemovedash(`${each?.status}`)}</span>
          </>
        ),
        reconciled_report: <>{each.description}</>,
        created_at: (
          <>
            <span>{getDate(each.created_at)}</span>
            <span className="annotation" style={{ marginLeft: '5px' }}>
              {getTime(each.created_at)}
            </span>
          </>
        ),
        action: (
          <span className={'action'} data-testid="amount">
            <Icon name="downloadInside" />
            Download
          </span>
        )
      }
    })
  };
  return (
    <section className="recon-container__table">
      <div className="recon-container__table--info">
        <p>Reconciled reports are available to download for 7 days, after which they are deleted permanently.</p>
      </div>
      <Table
        annotation="Reconciliation History"
        className={reconciliationHistory.className}
        data={reconciliationHistory.data || []}
        tableHeadings={['Status', 'Reconciled Report', 'Date Created', 'Action']}
        loading={false}
        renderFields
        hasPagination
        pageSize={Number(limit)}
        totalItems={data?.data?.paging?.total_items}
        current={parseInt(page, 10)}
        hideTable={reconciliationHistory.data?.length === 0}
        tableWrapperClassName="table-responsive table-wrapper"
        emptyStateHeading={reconciliationHistory?.emptyStateHeading || ''}
        emptyStateMessage={reconciliationHistory.emptyStateMessage || ''}
        actionFn={currentPage => searchQuery.setQuery({ page: String(currentPage) })}
        limitAction={currentLimit => searchQuery.setQuery({ limit: String(currentLimit) })}
        filterHasAdvancedFilter={false}
        type="reconciliation"
        filterShowExport={false}
        hasFilter={false}
      >
        {reconciliationHistory.fields}
      </Table>
    </section>
  );
};

export default ReconciliationHistoryTable;
