import React from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';
import { ReconciliationHistoryType } from '+types';
import { capitalizeRemovedash, getDate, getTime, switchStatus } from '+utils';

export const reconciliationHistory = {
  className: '--reconciliation-history-table',
  emptyStateHeading: 'No History yet',
  emptyStateMessage: 'Reconciliation History Not Available',
  fields: (each: ReconciliationHistoryType) => ({
    data: {
      status: (
        <>
          <span className={`status-pill smaller ${switchStatus(each.status)}`} />
          <span>{capitalizeRemovedash(`${each?.status}`)}</span>
        </>
      ),
      reconciled_report: <>{each?.title}</>,
      created_at: (
        <>
          <span>{getDate(each.createdAt)}</span>
          <span className="annotation" style={{ marginLeft: '5px' }}>
            {getTime(each.createdAt)}
          </span>
        </>
      ),
      action: each?.result_file_id && (
        <span className={'action'} data-testid="action">
          <Icon name="downloadInside" />
          Download
        </span>
      )
    }
  })
};
