import { ComparisonKeyMappingType, PrimaryKeyMappingType } from '+types';
import { capitalizeRemovedash } from '+utils';

export const buildReconciliationReportOptions = (
  primaryKeyMappings: PrimaryKeyMappingType,
  comparisonKeyMappings: ComparisonKeyMappingType
) => {
  const allProcessorReportOptions = primaryKeyMappings.map(item => ({
    label: capitalizeRemovedash(item.processor_report),
    value: item.processor_report,
    color: item.color
  }));
  const allInternalReportOptions = primaryKeyMappings.map(item => ({
    label: capitalizeRemovedash(item.internal_report),
    value: item.internal_report,
    color: item.color
  }));

  const processorReportOptions =
    comparisonKeyMappings.length === 0
      ? allProcessorReportOptions
      : allProcessorReportOptions.filter(item => comparisonKeyMappings.every(option => option?.processor_report !== item.value));

  const internalReportOptions =
    comparisonKeyMappings.length === 0
      ? allInternalReportOptions
      : allInternalReportOptions.filter(item => comparisonKeyMappings.every(option => option?.internal_report !== item.value));
  console.log('processorReportOptions', processorReportOptions, internalReportOptions, comparisonKeyMappings);
  return [processorReportOptions, internalReportOptions];
};
