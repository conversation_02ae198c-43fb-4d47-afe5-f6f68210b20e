import React from 'react';
import { Route, Switch } from 'react-router-dom';

import ReconcileReport from './ReconcileReport';
import ReconciliationHistory from './ReconciliationHistory';
import StartReconciliation from './StartReconciliation';

const Reconciliation = () => {
  return (
    <Switch>
      <Route path="/dashboard/reconciliation" exact component={ReconciliationHistory} />
      <Route path="/dashboard/reconciliation/start" exact component={StartReconciliation} />
      <Route path="/dashboard/reconciliation/reconcile" exact component={ReconcileReport} />
    </Switch>
  );
};

export default Reconciliation;
