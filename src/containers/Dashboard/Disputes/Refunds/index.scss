@import 'styles/kpy-custom/variables';

// Styles for the ReversalsInfoModal component:
.reversals-info-modal__comp {
  h3 {
    font-size: 1rem;
    font-weight: 400;
    color: rgb(62, 75, 91) !important;
    margin: 0.5rem 0 !important;
  }

  p {
    font-size: 0.9rem;
    color: rgba(62, 75, 91, 0.7);
  }

  .refunds-modal__p {
    border-radius: 5px;
    background: var(--neutral-gray-100, #f3f4f8);
    padding: 0.94rem;
    margin: 1.89rem 0.9rem;
  }

  a {
    font-size: 1rem;
    display: block;
    text-decoration: none !important;
    font-weight: 500;
    text-align: center !important;
    margin: 2rem 0 !important;
  }
}

#payment-detail {
  width: 50%;
  display: flex;
  flex-wrap: wrap;
  color: #2376f3;

  > span {
    font-style: normal;
    font-weight: 400;
    width: 40%;
  }

  .reversal-count {
    font-size: 13px;
    margin-bottom: 8px;
    font-weight: 600;
    padding: 8px 15px;

    @media (max-width: $breakpoint-tablet) {
      padding-left: 15px;
    }
  }

  @media (max-width: $breakpoint-tablet) {
    flex-direction: column;
    margin-left: 10px;
    padding-right: 10px;

    div {
      width: 100% !important;
      margin: 0;
    }
  }

  @media (min-width: $breakpoint-tablet) {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
  }
}

.transaction-details__comp_2 .transaction-details-container-2 .customer-information .customer-reversal-info {
  width: 60%;
  padding-left: 15px;

  ul {
    border-bottom: 1.5px dashed #dde2ec;

    li {
      display: flex;
      color: #414f5f;
      text-align: right;
      justify-content: space-between;
      margin-bottom: 0px;
      padding: 0px;

      > p {
        font-weight: 400;

        &:first-of-type {
          color: #a9afbc;
        }
      }
    }
  }
}

#payment-detail {
  width: 50%;
  display: flex;
  flex-wrap: wrap;

  .reversal-count-container {
    padding-right: 20px;
  }

  .full-width {
    width: 100%;
  }

  .reversal-count {
    color: #a9afbc;
    font-size: 13px;
    margin-bottom: 8px;
    font-weight: 400;
    padding: 8px 15px;

    @media (max-width: $breakpoint-tablet) {
      padding-left: 15px;
    }
  }

  .active-dispute {
    border-left: $kpyblue 3px solid;
  }

  .--container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
    display: flex;
    background: #f1f6fa;
    padding: 5px;
    margin: 0px 0px 7px 0px;

    :hover {
      cursor: pointer;
      opacity: 0.9;
    }

    p {
      margin-bottom: 0px !important;
    }

    .tab-icon {
      border-top: 3px solid #a9afbc;
      border-right: 3px solid #a9afbc;
      width: 10px;
      height: 10px;
      transform: rotate(45deg);
      margin-right: 10px;

      @media (max-width: $breakpoint-desktop) {
        display: none;
      }
    }

    .tab-icon.active {
      border-color: #2376f3;
    }

    .reversal-amount {
      background: #f1f6fa;
      padding: 10px;
      margin: 0px 0px 7px 0px;
      color: #2376f3;

      > label {
        font-size: 14px;
        width: max-content !important;
        color: #2376f3;
        font-weight: 300 !important;
        margin-right: 5px !important;
      }
    }
  }

  @media (max-width: $breakpoint-tablet) {
    width: 100%;
    gap: 2rem;
    flex-direction: column;
    margin-left: 10px;
    padding-right: 10px;

    div {
      width: 100% !important;
      margin: 0;
    }

    .reversal-amount {
      margin-left: 5px;
      width: 98%;
    }
  }

  @media (min-width: $breakpoint-tablet) {
    width: 100%;
    display: flex;
    flex-wrap: wrap;

    .reversal-amount {
      width: 100%;
    }
  }
}

.customer-reversal-info {
  padding-left: 15px;

  ul {
    border-bottom: 1.5px dashed #dde2ec;

    li {
      display: flex;
      color: #414f5f;
      text-align: right;
      justify-content: space-between;
      margin-bottom: 0px !important;
      padding: 0px !important;

      > p {
        font-weight: 400 !important;

        &:first-of-type {
          color: #a9afbc;
        }

        &:last-of-type {
          max-width: 60%;
          cursor: default;
        }
      }
    }
  }

  .reversal_ref {
    color: #2376f3;
    font-weight: 600;
    font-size: 0.9rem;
  }
}

.no-refund {
  font-size: 13px !important;
  font-weight: 300 !important;
  color: #a9afbc !important;
  font-style: italic;
  margin-left: 1rem;
}

.reversal-state {
  margin: 1.875rem;

  .reversal-state__container {
    display: flex;
    height: 18.75rem;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #f9fbfd;
    border-radius: 16px;

    img {
      width: 30px;
      height: 30px;
      flex-shrink: 0;
    }

    .reversal-state__title {
      font-size: 16px;
      color: #292b2c;
      text-align: center;
      width: 230px;
      margin: 30px 0 5px 0;
    }

    .reversal-state__subtitle {
      font-size: 14px;
      font-style: normal;
      color: #94a7b7;
      text-align: center;
      width: 230px;
    }

    button {
      background: none;
      border: none;
      outline: none;
      margin-top: 15px;
      cursor: pointer;
      display: flex;
      align-items: center;

      img {
        width: 20px;
        height: 20px;
        margin-right: 12px;
      }

      span {
        font-size: 17px;
        font-weight: 500;
        color: #2376f3;
      }
    }
  }
}

.refunds-radio_container {
  display: flex;
  flex-direction: column;
  padding: 0.5rem 1.3rem;

  label {
    display: flex;
    align-items: center;
    column-gap: 5px;
    margin-bottom: 10px;
    font-family: 'Averta PE';
    font-weight: 400;
    color: #3e4b5b;
  }
}

.refund-modal__hr {
  margin-left: -23px;
  margin-right: -16px;
  color: #0000001a;
}

.reversal-pg-route {
  all: unset;
  cursor: pointer;
  color: #007bff;
  display: block;
  font-weight: 500;
  margin: 0 0 1rem auto;
  letter-spacing: 0.2px;
  text-align: right;
}

.breakdown-modal {
  list-style-type: none;
  padding: 0;
  margin: 0;

  li {
    display: flex;
    justify-content: space-between;
    margin: 0.75rem 0 0;
    padding: 0;

    span:nth-child(1) {
      align-items: center;
      color: #636c72;
      display: flex;
      font-weight: 500;
      gap: 0.4rem;
    }

    span:nth-child(2) {
      color: #636c7294;
      font-weight: 400;
    }

    span.total {
      color: #102649;
    }
  }
}

.processor_message_container {
  margin: 5px 0 0 0;
  background: #F1F6FA;
  padding: 15px;
  border-radius: 5px;
}
