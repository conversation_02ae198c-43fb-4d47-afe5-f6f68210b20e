import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { chargebackActivityUpdateT } from '+types';

import ChargebackActivityUpdate from '../ChargebackActivityUpdate';

const resolveAction = vi.fn();
const acceptedChargebackData = {
  account: {
    email: '<EMAIL>',
    name: 'Tuns'
  },
  account_id: 105,
  amount: '50.00',
  approved_amount: '50.00',
  batch_code: 'KPY-CHG-BTCH-aABn4GVFjaCJsOu',
  created_at: '2023-06-08 15:33:08',
  currency: 'NGN',
  deadline: '2023-06-09T15:33:00.000Z',
  log_code: null,
  merchant: 'Tuns',
  merchant_email: '<EMAIL>',
  payment_method: 'card',
  payment_reference: 'KPY-PAY-SCYgHyBIDchn',
  payment_reversal_reference: 'KPY-CHB-IaQuZSTWyKifu',
  payment_source_reference: 'KPY-CM-vrU7V4iwgC8h',
  processor: 'interswitch',
  processor_reference: 'isc-w9CAUfi-ext',
  reason: 'nope',
  reference: 'KPY-CHG-9qWflyGluWwPvBS',
  source: {
    details: {
      masked_pan: '************7571',
      card_type: 'mastercard'
    },
    type: 'card'
  },
  status: 'accepted',
  status_history: [
    {
      date: '2023-06-08T22:53:49.790Z',
      status: 'accepted'
    }
  ]
};

const pendingChargebackData = {
  ...acceptedChargebackData,
  status: 'pending'
};

const partialChargebackData = {
  ...acceptedChargebackData,
  status: 'partial',
  merchant: 'damiworkTest',
  account: {
    ...acceptedChargebackData.account,
    name: 'damiworkTest'
  },
  status_history: [
    {
      ...acceptedChargebackData.status_history[0],
      status: 'partial',
      evidence_link: new URL('https://api.koraapi.com/utilities/api/files/Evidence.pdf')
    }
  ]
};
function MockedChargebackActivityUpdate({ value }: { value: chargebackActivityUpdateT['data'] }) {
  return (
    <MockIndex>
      <ChargebackActivityUpdate resolveAction={resolveAction} data={value} />
    </MockIndex>
  );
}
describe('ChargebackActivityUpdate', () => {
  it('should render successfully', () => {
    render(<MockedChargebackActivityUpdate value={acceptedChargebackData as chargebackActivityUpdateT['data']} />);
    expect(screen.getByText('-- Chargeback accepted --')).toBeInTheDocument();
  });
  it('should render successfully with pending state', () => {
    render(<MockedChargebackActivityUpdate value={pendingChargebackData as chargebackActivityUpdateT['data']} />);
    expect(screen.getByText('Evidence.pdf')).toBeInTheDocument();
    expect(screen.getByText('Tuns · Tuns defended this chargeback')).toBeInTheDocument();
  });
  it('should render successfully with partial state', () => {
    render(<MockedChargebackActivityUpdate value={partialChargebackData as chargebackActivityUpdateT['data']} />);
    expect(screen.getByText('Evidence.pdf')).toBeInTheDocument();
    expect(screen.getByText('-- Partial Chargeback --')).toBeInTheDocument();
    expect(screen.getByText('Download')).toBeInTheDocument();
    expect(screen.getByText('Damiworktest · Damiworktest defended this chargeback')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'download-file Download' })).toBeInTheDocument();
  });
});
