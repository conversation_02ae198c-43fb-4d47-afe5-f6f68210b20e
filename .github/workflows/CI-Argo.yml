name: Admin Dashboard CI/CD

on:
  pull_request:
    branches: [staging, release, production, preprod]
  push:
    branches: [staging, release, production, preprod]

jobs:
  # build_deploy_release:
  #   if: ${{ github.ref == 'refs/heads/release' }}
  #   uses: korapay/infra-workflows/.github/workflows/build_deploy.yaml@main
  #   secrets: inherit
  #   with:
  #     node-version: 18.x
  #     service: business-admin-fe
  #     envtype: release 
  #     runner-path: '/home/<USER>/korapay-client/admin'
  #     cluster: gaia
  #     argocd-service: businessadminfe
  #     argocd-service-prefix: 01-gaia-eks

  build_deploy_preprod:
    if: ${{ github.ref == 'refs/heads/preprod' }}
    uses: korapay/infra-workflows/.github/workflows/build_deploy.yaml@main
    secrets: inherit
    with:
      node-version: 18.x
      service: business-admin-fe
      envtype: release # change to preprod after golive
      runner-path: '/home/<USER>/korapay-client/admin'
      cluster: hera
      argocd-service: businessadminfe
      argocd-service-prefix: 01-hera-eks

  build_deploy_production:
    if: ${{ github.ref == 'refs/heads/production' }}
    uses: korapay/infra-workflows/.github/workflows/build_deploy.yaml@main
    secrets: inherit
    with:
      node-version: 18.x
      service: business-admin-fe
      envtype: release # change to production after golive
      runner-path: '/home/<USER>/korapay-client/admin'
      cluster: gaia
      argocd-service: businessadminfe
      argocd-service-prefix: 02-gaia-eks
